<template>
  <div id="modalLoading" class="modalL">
    <div class="modal-contentL">
      <LoadingIcon icon="ball-triangle" color="#FFFFFF" />
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';

  const isVisible = ref(false);

  const show = () => {
    isVisible.value = true;
    document.getElementById('modalLoading').style.display = 'block';
  };

  const hide = () => {
    isVisible.value = false;
    document.getElementById('modalLoading').style.display = 'none';
  };

  defineExpose({ show, hide });
</script>

<style scoped>
  .modalL {
    display: none;
    position: fixed;
    z-index: 99999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    justify-content: center;
    align-items: center;
  }

  .modal-contentL {
    height: 100px;
    width: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
