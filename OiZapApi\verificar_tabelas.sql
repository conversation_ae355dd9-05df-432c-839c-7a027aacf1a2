-- Script para verificar se as tabelas do sistema de controle de acesso existem

-- Verificar se as tabelas existem
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('perfis', 'telas', 'perfis_telas', 'usuarios_perfis')
ORDER BY table_name;

-- Verificar estrutura da tabela perfis
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'perfis'
ORDER BY ordinal_position;

-- Verificar estrutura da tabela telas
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'telas'
ORDER BY ordinal_position;

-- Verificar estrutura da tabela perfis_telas
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'perfis_telas'
ORDER BY ordinal_position;

-- Verificar estrutura da tabela usuarios_perfis
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'usuarios_perfis'
ORDER BY ordinal_position;

-- Verificar dados existentes
SELECT 'perfis' as tabela, count(*) as total FROM perfis
UNION ALL
SELECT 'telas' as tabela, count(*) as total FROM telas
UNION ALL
SELECT 'perfis_telas' as tabela, count(*) as total FROM perfis_telas
UNION ALL
SELECT 'usuarios_perfis' as tabela, count(*) as total FROM usuarios_perfis;

-- Verificar se o usuário 87 (Robson) tem perfis associados
SELECT 
    up.cd_usuario_perfil,
    up.cd_usuario,
    up.cd_perfil,
    p.nm_perfil,
    up.created_at
FROM usuarios_perfis up
JOIN perfis p ON p.cd_perfil = up.cd_perfil
WHERE up.cd_usuario = 87;

-- Verificar telas associadas ao perfil do usuário 87
SELECT DISTINCT
    t.cd_tela,
    t.nm_tela,
    t.ds_rota,
    pt.in_visualizar,
    pt.in_inserir,
    pt.in_alterar,
    pt.in_excluir
FROM usuarios_perfis up
JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE up.cd_usuario = 87
ORDER BY t.nm_tela;
