-- Dados de exemplo para demonstração do sistema de gestão de módulos e telas

-- Inserir módulos de exemplo
INSERT INTO modulos (nm_modulo, ds_funcionalidade, in_ativo) VALUES
('Administração', 'Módulo de administração do sistema com gestão de usuários, perfis e permissões', true),
('Atendimento', 'Módulo de atendimento ao cliente com chat, tickets e histórico', true),
('Vendas', 'Módulo de vendas com pedidos, produtos e relatórios', true),
('Relatórios', 'Módulo de relatórios e dashboards analíticos', true),
('Configurações', 'Módulo de configurações gerais do sistema', true)
ON CONFLICT (nm_modulo) DO NOTHING;

-- Inserir telas de exemplo para o módulo Administração (cd_modulo = 1)
INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo) VALUES
('Gestão de Usuários', '/usuarios', 'Gerenciar usuários do sistema', true, 1),
('Gestão de Perfis', '/perfis', 'Gerenciar perfis de acesso', true, 1),
('Gestão de Telas', '/telas', 'Gerenciar telas do sistema', true, 1),
('Gestão de Módulos', '/gestao-modulos', 'Gerenciar módulos do sistema', true, 1),
('Gestão de Permissões', '/gestao-perfis-modulos', 'Gerenciar permissões por módulos', true, 1)
ON CONFLICT (ds_rota) DO NOTHING;

-- Inserir telas de exemplo para o módulo Atendimento (cd_modulo = 2)
INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo) VALUES
('Chat em Tempo Real', '/chat', 'Interface de chat para atendimento', true, 2),
('Histórico de Atendimentos', '/atendimentos', 'Visualizar histórico de atendimentos', true, 2),
('Fila de Atendimento', '/fila-atendimento', 'Gerenciar fila de atendimento', true, 2),
('Relatório de Atendimento', '/relatorio-atendimento', 'Relatórios de performance de atendimento', true, 2)
ON CONFLICT (ds_rota) DO NOTHING;

-- Inserir telas de exemplo para o módulo Vendas (cd_modulo = 3)
INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo) VALUES
('Gestão de Produtos', '/produtos', 'Gerenciar catálogo de produtos', true, 3),
('Pedidos', '/pedidos', 'Gerenciar pedidos de venda', true, 3),
('Clientes', '/clientes', 'Gerenciar cadastro de clientes', true, 3),
('Carrinho de Compras', '/carrinho', 'Interface do carrinho de compras', true, 3)
ON CONFLICT (ds_rota) DO NOTHING;

-- Inserir telas de exemplo para o módulo Relatórios (cd_modulo = 4)
INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo) VALUES
('Dashboard Principal', '/dashboard', 'Dashboard com métricas principais', true, 4),
('Relatório de Vendas', '/relatorio-vendas', 'Relatórios detalhados de vendas', true, 4),
('Relatório de Usuários', '/relatorio-usuarios', 'Relatórios de atividade dos usuários', true, 4),
('Analytics', '/analytics', 'Análises avançadas e métricas', true, 4)
ON CONFLICT (ds_rota) DO NOTHING;

-- Inserir telas de exemplo para o módulo Configurações (cd_modulo = 5)
INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo) VALUES
('Configurações Gerais', '/configuracoes', 'Configurações gerais do sistema', true, 5),
('Parâmetros', '/parametros', 'Gerenciar parâmetros do sistema', true, 5),
('Backup e Restore', '/backup', 'Funcionalidades de backup e restore', true, 5),
('Logs do Sistema', '/logs', 'Visualizar logs do sistema', true, 5)
ON CONFLICT (ds_rota) DO NOTHING;

-- Criar alguns perfis de exemplo se não existirem
INSERT INTO perfis (nm_perfil, ds_perfil, in_ativo) VALUES
('Administrador', 'Perfil com acesso total ao sistema', true),
('Gerente', 'Perfil de gerência com acesso a relatórios e gestão', true),
('Atendente', 'Perfil para atendimento ao cliente', true),
('Vendedor', 'Perfil para vendas e gestão de produtos', true),
('Visualizador', 'Perfil apenas para visualização de dados', true)
ON CONFLICT (nm_perfil) DO NOTHING;

-- Associar o perfil Administrador a todas as telas com permissões completas
INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir)
SELECT 
    p.cd_perfil,
    t.cd_tela,
    true,
    true,
    true,
    true
FROM perfis p
CROSS JOIN telas t
WHERE p.nm_perfil = 'Administrador'
ON CONFLICT (cd_perfil, cd_tela) DO NOTHING;

-- Associar o perfil Gerente a módulos específicos
INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir)
SELECT 
    p.cd_perfil,
    t.cd_tela,
    true,
    CASE WHEN m.nm_modulo IN ('Relatórios', 'Vendas') THEN true ELSE false END,
    CASE WHEN m.nm_modulo IN ('Vendas') THEN true ELSE false END,
    false
FROM perfis p
CROSS JOIN telas t
JOIN modulos m ON t.cd_modulo = m.cd_modulo
WHERE p.nm_perfil = 'Gerente'
AND m.nm_modulo IN ('Relatórios', 'Vendas', 'Atendimento')
ON CONFLICT (cd_perfil, cd_tela) DO NOTHING;

-- Associar o perfil Atendente ao módulo de Atendimento
INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir)
SELECT 
    p.cd_perfil,
    t.cd_tela,
    true,
    true,
    true,
    false
FROM perfis p
CROSS JOIN telas t
JOIN modulos m ON t.cd_modulo = m.cd_modulo
WHERE p.nm_perfil = 'Atendente'
AND m.nm_modulo = 'Atendimento'
ON CONFLICT (cd_perfil, cd_tela) DO NOTHING;

-- Associar o perfil Vendedor ao módulo de Vendas
INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir)
SELECT 
    p.cd_perfil,
    t.cd_tela,
    true,
    true,
    true,
    false
FROM perfis p
CROSS JOIN telas t
JOIN modulos m ON t.cd_modulo = m.cd_modulo
WHERE p.nm_perfil = 'Vendedor'
AND m.nm_modulo IN ('Vendas', 'Atendimento')
ON CONFLICT (cd_perfil, cd_tela) DO NOTHING;

-- Associar o perfil Visualizador apenas para visualização
INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir)
SELECT 
    p.cd_perfil,
    t.cd_tela,
    true,
    false,
    false,
    false
FROM perfis p
CROSS JOIN telas t
JOIN modulos m ON t.cd_modulo = m.cd_modulo
WHERE p.nm_perfil = 'Visualizador'
AND m.nm_modulo IN ('Relatórios', 'Dashboard')
ON CONFLICT (cd_perfil, cd_tela) DO NOTHING;
