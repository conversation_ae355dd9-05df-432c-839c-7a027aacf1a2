-- Migration: create_table_modulos
-- Created: 2025-01-09T02:00:00.000Z
-- Environment: ALL

-- ========================================
-- UP: Criar tabela modulos
-- ========================================

-- <PERSON><PERSON>r tabela modulos
CREATE TABLE IF NOT EXISTS modulos (
  cd_modulo SERIAL PRIMARY KEY,
  nm_modulo VARCHAR(100) NOT NULL UNIQUE,
  ds_funcionalidade TEXT,
  vl_modulo DECIMAL(10,2) DEFAULT 0.00,
  in_ativo BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Criar índices
CREATE INDEX IF NOT EXISTS idx_modulos_nm_modulo ON modulos(nm_modulo);
CREATE INDEX IF NOT EXISTS idx_modulos_in_ativo ON modulos(in_ativo);

-- Comentários
COMMENT ON TABLE modulos IS 'Tabela de módulos do sistema';
COMMENT ON COLUMN modulos.cd_modulo IS 'Código único do módulo';
COMMENT ON COLUMN modulos.nm_modulo IS 'Nome do módulo (único)';
COMMENT ON COLUMN modulos.ds_funcionalidade IS 'Descrição das funcionalidades do módulo';
COMMENT ON COLUMN modulos.vl_modulo IS 'Valor do módulo';
COMMENT ON COLUMN modulos.in_ativo IS 'Indica se o módulo está ativo';

-- ========================================
-- DOWN: Remover estrutura
-- ========================================

-- Para reverter a migration (se necessário):
-- DROP TABLE IF EXISTS modulos CASCADE;
