-- <PERSON><PERSON><PERSON> para criar e configurar o sistema de controle de acesso
-- Execute este script no seu banco de dados PostgreSQL

-- 1. <PERSON>riar tabela de perfis
CREATE TABLE IF NOT EXISTS perfis (
    cd_perfil SERIAL PRIMARY KEY,
    nm_perfil VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Criar tabela de telas
CREATE TABLE IF NOT EXISTS telas (
    cd_tela SERIAL PRIMARY KEY,
    nm_tela VARCHAR(100) NOT NULL,
    ds_rota VARCHAR(255) NOT NULL UNIQUE,
    ds_descricao TEXT,
    in_ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. <PERSON><PERSON><PERSON> tabela de associações perfil-tela
CREATE TABLE IF NOT EXISTS perfis_telas (
    cd_perfil_tela SERIAL PRIMARY KEY,
    cd_perfil INTEGER NOT NULL REFERENCES perfis(cd_perfil) ON DELETE CASCADE,
    cd_tela INTEGER NOT NULL REFERENCES telas(cd_tela) ON DELETE CASCADE,
    in_visualizar BOOLEAN DEFAULT false,
    in_inserir BOOLEAN DEFAULT false,
    in_alterar BOOLEAN DEFAULT false,
    in_excluir BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(cd_perfil, cd_tela)
);

-- 4. Criar tabela de associações usuário-perfil
CREATE TABLE IF NOT EXISTS usuarios_perfis (
    cd_usuario_perfil SERIAL PRIMARY KEY,
    cd_usuario INTEGER NOT NULL,
    cd_perfil INTEGER NOT NULL REFERENCES perfis(cd_perfil) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(cd_usuario, cd_perfil)
);

-- 5. Inserir perfil administrativo
INSERT INTO perfis (nm_perfil) VALUES ('Administrador') 
ON CONFLICT (nm_perfil) DO NOTHING;

-- 6. Inserir telas administrativas
INSERT INTO telas (nm_tela, ds_rota, ds_descricao) VALUES 
    ('Gestão de Perfis', '/oizap/gestao-perfis', 'Gerenciar perfis de acesso do sistema'),
    ('Gestão de Telas', '/oizap/gestao-telas', 'Gerenciar telas disponíveis no sistema'),
    ('Perfis x Telas', '/oizap/gestao-perfis-telas', 'Associar perfis às telas com permissões'),
    ('Usuários x Perfis', '/oizap/gestao-usuarios-perfis', 'Associar usuários aos perfis de acesso')
ON CONFLICT (ds_rota) DO NOTHING;

-- 7. Associar perfil administrativo às telas (com todas as permissões)
INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir)
SELECT 
    p.cd_perfil,
    t.cd_tela,
    true,
    true,
    true,
    true
FROM perfis p
CROSS JOIN telas t
WHERE p.nm_perfil = 'Administrador'
AND t.ds_rota IN ('/oizap/gestao-perfis', '/oizap/gestao-telas', '/oizap/gestao-perfis-telas', '/oizap/gestao-usuarios-perfis')
ON CONFLICT (cd_perfil, cd_tela) DO NOTHING;

-- 8. Associar usuário 87 (Robson) ao perfil administrativo
INSERT INTO usuarios_perfis (cd_usuario, cd_perfil)
SELECT 87, cd_perfil
FROM perfis
WHERE nm_perfil = 'Administrador'
ON CONFLICT (cd_usuario, cd_perfil) DO NOTHING;

-- 9. Verificar se tudo foi criado corretamente
SELECT 'Verificação dos dados criados:' as status;

SELECT 'Perfis:' as tabela, count(*) as total FROM perfis;
SELECT 'Telas:' as tabela, count(*) as total FROM telas;
SELECT 'Perfis-Telas:' as tabela, count(*) as total FROM perfis_telas;
SELECT 'Usuários-Perfis:' as tabela, count(*) as total FROM usuarios_perfis;

-- 10. Verificar dados do usuário 87
SELECT 
    'Perfis do usuário 87:' as info,
    up.cd_usuario,
    p.nm_perfil
FROM usuarios_perfis up
JOIN perfis p ON p.cd_perfil = up.cd_perfil
WHERE up.cd_usuario = 87;

-- 11. Verificar telas do usuário 87
SELECT 
    'Telas do usuário 87:' as info,
    t.nm_tela,
    t.ds_rota,
    pt.in_visualizar,
    pt.in_inserir,
    pt.in_alterar,
    pt.in_excluir
FROM usuarios_perfis up
JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
JOIN telas t ON t.cd_tela = pt.cd_tela
WHERE up.cd_usuario = 87
ORDER BY t.nm_tela;
