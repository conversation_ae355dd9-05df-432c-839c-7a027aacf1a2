require('dotenv').config();
const { Client } = require('pg');

const environment = process.env.NODE_ENV || 'DEV';

const getConnectionConfig = (env) => {
  const configs = {
    DEV: {
      host: process.env.HOST_DATABASEDEV,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASEDEV,
      database: process.env.DATABASE_DEV,
      port: Number(process.env.PORT_DATABASEDEV),
    },
    CRM: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_CRM,
      port: Number(process.env.PORT_DATABASE),
    },
    SANDBOX: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE_SANDBOX,
      port: Number(process.env.PORT_DATABASE),
    },
    PROD: {
      host: process.env.HOST_DATABASE,
      user: process.env.USER_DATABASE,
      password: process.env.PASSWORD_DATABASE,
      database: process.env.DATABASE,
      port: Number(process.env.PORT_DATABASE),
    },
  };
  return configs[env];
};

async function setupProfileForUser(client, userId, userName) {
  // 2. Verificar se já existe um perfil administrativo
  let adminProfile = await client.query(
    "SELECT cd_perfil, nm_perfil FROM perfis WHERE LOWER(nm_perfil) LIKE '%admin%' OR LOWER(nm_perfil) LIKE '%administr%'"
  );

  let adminProfileId;

  if (adminProfile.rows.length === 0) {
    // Criar perfil administrativo
    console.log('📝 Criando perfil administrativo...');
    const newProfile = await client.query(
      'INSERT INTO perfis (nm_perfil) VALUES ($1) RETURNING cd_perfil, nm_perfil',
      ['Administrador']
    );

    adminProfileId = newProfile.rows[0].cd_perfil;
    console.log(`✅ Perfil administrativo criado: ${newProfile.rows[0].nm_perfil} (ID: ${adminProfileId})`);
  } else {
    adminProfileId = adminProfile.rows[0].cd_perfil;
    console.log(`✅ Perfil administrativo encontrado: ${adminProfile.rows[0].nm_perfil} (ID: ${adminProfileId})`);
  }

  // 3. Verificar se o usuário já possui este perfil
  const existingAssociation = await client.query(
    'SELECT id FROM usuarios_perfis WHERE cd_usuario = $1 AND cd_perfil = $2',
    [userId, adminProfileId]
  );

  if (existingAssociation.rows.length > 0) {
    console.log(`ℹ️  Usuário ${userId} já possui o perfil administrativo`);
  } else {
    // Criar associação usuário-perfil
    console.log('🔗 Associando usuário ao perfil administrativo...');
    await client.query(
      'INSERT INTO usuarios_perfis (cd_usuario, cd_perfil) VALUES ($1, $2)',
      [userId, adminProfileId]
    );
    console.log(`✅ Usuário ${userId} associado ao perfil administrativo com sucesso!`);
  }

  // 4. Mostrar resumo final
  console.log('\n📋 Resumo da configuração:');
  const finalCheck = await client.query(`
    SELECT
      u.cd_usuario,
      u.ds_nome,
      p.cd_perfil,
      p.nm_perfil,
      up.created_at as data_associacao
    FROM adm_usuarios u
    JOIN usuarios_perfis up ON u.cd_usuario = up.cd_usuario
    JOIN perfis p ON up.cd_perfil = p.cd_perfil
    WHERE u.cd_usuario = $1
  `, [userId]);

  finalCheck.rows.forEach(row => {
    console.log(`   👤 Usuário: ${row.ds_nome} (ID: ${row.cd_usuario})`);
    console.log(`   🎭 Perfil: ${row.nm_perfil} (ID: ${row.cd_perfil})`);
    console.log(`   📅 Associado em: ${row.data_associacao}`);
  });

  console.log('\n🎉 Configuração concluída com sucesso!');
}

async function setupAdminUser() {
  const config = getConnectionConfig(environment);
  const client = new Client(config);

  try {
    await client.connect();
    console.log(`🚀 Configurando usuário administrativo - Ambiente: ${environment}`);

    const userId = 87;

    // 1. Verificar se o usuário existe
    const userCheck = await client.query(
      'SELECT cd_usuario, ds_nome FROM adm_usuarios WHERE cd_usuario = $1',
      [userId]
    );

    if (userCheck.rows.length === 0) {
      console.log(`❌ Usuário com cd_usuario = ${userId} não encontrado`);

      // Listar usuários existentes
      const existingUsers = await client.query(
        'SELECT cd_usuario, ds_nome, ds_login FROM adm_usuarios ORDER BY cd_usuario LIMIT 10'
      );

      if (existingUsers.rows.length > 0) {
        console.log('\n📋 Usuários existentes no sistema:');
        existingUsers.rows.forEach(user => {
          console.log(`   - ID: ${user.cd_usuario}, Nome: ${user.ds_nome}, Login: ${user.ds_login}`);
        });

        // Usar o primeiro usuário encontrado
        const firstUser = existingUsers.rows[0];
        console.log(`\n🔄 Usando o usuário existente: ${firstUser.ds_nome} (ID: ${firstUser.cd_usuario})`);

        // Atualizar userId para o primeiro usuário encontrado
        const actualUserId = firstUser.cd_usuario;

        // Continuar com o usuário encontrado
        console.log(`✅ Usuário selecionado: ${firstUser.ds_nome} (ID: ${actualUserId})`);

        // Atualizar a variável userId
        const finalUserId = actualUserId;

        // Prosseguir com a configuração usando finalUserId
        await setupProfileForUser(client, finalUserId, firstUser.ds_nome);
        return;
      } else {
        console.log('\n❌ Nenhum usuário encontrado no sistema');
        console.log('📝 Criando usuário administrativo...');

        // Criar usuário administrativo
        const newUser = await client.query(`
          INSERT INTO adm_usuarios (
            ds_nome,
            ds_login,
            ds_email,
            ds_senha,
            tp_privilegio,
            tp_status,
            dt_cadastro
          ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
          RETURNING cd_usuario, ds_nome
        `, [
          'Administrador Sistema',
          'admin',
          '<EMAIL>',
          '$2b$10$rQJ8vQZ9QZ9QZ9QZ9QZ9QOZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ', // senha: admin123
          'A', // Administrador
          'A'  // Ativo
        ]);

        const createdUser = newUser.rows[0];
        console.log(`✅ Usuário administrativo criado: ${createdUser.ds_nome} (ID: ${createdUser.cd_usuario})`);
        console.log('   📧 Email: <EMAIL>');
        console.log('   🔑 Senha: admin123');

        await setupProfileForUser(client, createdUser.cd_usuario, createdUser.ds_nome);
        return;
      }
    }

    console.log(`✅ Usuário encontrado: ${userCheck.rows[0].ds_nome} (ID: ${userId})`);
    await setupProfileForUser(client, userId, userCheck.rows[0].ds_nome);

  } catch (error) {
    console.error('❌ Erro ao configurar usuário administrativo:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

setupAdminUser();
