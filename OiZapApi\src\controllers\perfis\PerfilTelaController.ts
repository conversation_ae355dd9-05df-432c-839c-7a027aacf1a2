import { Request, Response } from 'express';
import { BAD_REQUEST, INTERNAL_SERVER_ERROR, parametrosInvalidos, erroInterno } from '../../interfaces/IRetorno';
import { PerfisTelaModel } from '../../models/perfis/PerfisTelaModel';

export class PerfilTelaController {
  static async associarPerfilTela(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.cd_perfil) {
        errors.push('O campo "cd_perfil" é obrigatório.');
      }
      
      if (!req.body.cd_tela) {
        errors.push('O campo "cd_tela" é obrigatório.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new PerfisTelaModel().associar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async desassociarPerfilTela(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.cd_perfil) {
        errors.push('O campo "cd_perfil" é obrigatório.');
      }
      
      if (!req.body.cd_tela) {
        errors.push('O campo "cd_tela" é obrigatório.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new PerfisTelaModel().desassociar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async atualizarPermissoes(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.cd_perfil) {
        errors.push('O campo "cd_perfil" é obrigatório.');
      }
      
      if (!req.body.cd_tela) {
        errors.push('O campo "cd_tela" é obrigatório.');
      }
      
      // Validar que pelo menos uma permissão foi informada
      const permissoes = ['in_visualizar', 'in_inserir', 'in_alterar', 'in_excluir'];
      const permissoesInformadas = permissoes.filter(p => req.body[p] !== undefined);
      
      if (permissoesInformadas.length === 0) {
        errors.push('Pelo menos uma permissão deve ser informada.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new PerfisTelaModel().atualizarPermissoes(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async listarAssociacoes(req: Request, res: Response): Promise<Response> {
    try {
      console.log('🔍 [DEBUG] PerfilTelaController.listarAssociacoes - Iniciando');
      console.log('🔍 [DEBUG] req.query:', req.query);

      const result = await new PerfisTelaModel().listar(req);

      console.log('🔍 [DEBUG] PerfilTelaController.listarAssociacoes - Resultado:', {
        statuscode: result.statuscode,
        dataLength: result.data?.length
      });

      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      console.log('❌ [ERROR] PerfilTelaController.listarAssociacoes:', error);
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async buscarAssociacaoPorId(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.params.nr_perfil_tela) {
        errors.push('O parâmetro "nr_perfil_tela" é obrigatório.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new PerfisTelaModel().buscarPorId(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async associarMultiplasTelas(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.cd_perfil) {
        errors.push('O campo "cd_perfil" é obrigatório.');
      }
      
      if (!req.body.telas || !Array.isArray(req.body.telas)) {
        errors.push('O campo "telas" deve ser um array.');
      }
      
      if (req.body.telas && req.body.telas.length === 0) {
        errors.push('O array "telas" deve conter pelo menos uma tela.');
      }
      
      // Validar estrutura das telas
      if (req.body.telas && Array.isArray(req.body.telas)) {
        req.body.telas.forEach((tela: any, index: number) => {
          if (!tela.cd_tela) {
            errors.push(`Tela ${index + 1}: O campo "cd_tela" é obrigatório.`);
          }
        });
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new PerfisTelaModel().associarMultiplas(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
