import { defineStore } from 'pinia';

export const useSimpleMenuStore = defineStore('simpleMenu', {
    state: () => ({
        menu: [],
    }),
    actions: {
        setMenu(newMenu) {
            console.log('🔍 [DEBUG] SimpleMenuStore.setMenu - Atualizando menu');
            console.log('🔍 [DEBUG] SimpleMenuStore.setMenu - Menu anterior:', this.menu);
            console.log('🔍 [DEBUG] SimpleMenuStore.setMenu - Novo menu:', newMenu);

            this.menu = newMenu;

            console.log('🔍 [DEBUG] SimpleMenuStore.setMenu - Menu atualizado:', this.menu);

            // Verificar se há item de Administração
            const itemAdmin = this.menu.find(item => item.title === 'Administração');
            if (itemAdmin) {
                console.log('✅ [DEBUG] SimpleMenuStore.setMenu - Item Administração encontrado no store:', itemAdmin);
            } else {
                console.log('❌ [DEBUG] SimpleMenuStore.setMenu - Item Administração NÃO encontrado no store');
            }
        }
    },
    persist: true,
});
