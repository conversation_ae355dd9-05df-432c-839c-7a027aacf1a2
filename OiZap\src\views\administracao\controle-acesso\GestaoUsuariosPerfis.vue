<template>
    <div class="intro-y flex flex-col sm:flex-row items-center mt-8">
        <h2 class="text-lg font-medium mr-auto">Gestão de Usuários x Perfis</h2>
        <div class="w-full sm:w-auto flex mt-4 sm:mt-0">
            <select v-model="usuarioSelecionado" class="form-select w-auto mr-2" @change="carregarAssociacoes">
                <option value="">Selecione um usuário</option>
                <option v-for="usuario in usuarios" :key="usuario.cd_usuario" :value="usuario.cd_usuario">
                    {{ usuario.ds_nome }} ({{ usuario.ds_login }})
                </option>
            </select>
            <button 
                @click="abrirModalAssociar" 
                class="btn btn-primary shadow-md"
                :disabled="!usuarioSelecionado"
            >
                <PlusIcon class="w-4 h-4 mr-2" />
                Associar Perfil
            </button>
        </div>
    </div>

    <!-- Informações do Usuário -->
    <div v-if="usuarioAtual" class="intro-y box p-5 mt-5">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mr-4">
                <UserIcon class="w-6 h-6 text-primary" />
            </div>
            <div>
                <h3 class="text-lg font-medium">{{ usuarioAtual.ds_nome }}</h3>
                <p class="text-slate-500">{{ usuarioAtual.ds_login }} • {{ associacoes.length }} perfil(s) associado(s)</p>
            </div>
        </div>
    </div>

    <!-- Lista de Associações -->
    <div v-if="usuarioSelecionado" class="intro-y grid grid-cols-12 gap-6 mt-5">
        <div class="col-span-12">
            <div class="intro-y box">
                <div class="flex flex-col sm:flex-row items-center p-5 border-b border-slate-200/60">
                    <h2 class="font-medium text-base mr-auto">Perfis Associados</h2>
                    <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                        <input
                            v-model="filtro"
                            type="text"
                            class="form-control w-full sm:w-64"
                            placeholder="Buscar perfil..."
                        />
                    </div>
                </div>
                <div class="p-5">
                    <div v-if="associacoes.length === 0" class="text-center py-8">
                        <UsersIcon class="w-16 h-16 mx-auto text-slate-300 mb-4" />
                        <p class="text-slate-500">Nenhum perfil associado a este usuário</p>
                        <button @click="abrirModalAssociar" class="btn btn-primary mt-4">
                            Associar primeiro perfil
                        </button>
                    </div>
                    <div v-else class="overflow-x-auto">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th class="whitespace-nowrap">Perfil</th>
                                    <th class="whitespace-nowrap">Total de Telas</th>
                                    <th class="whitespace-nowrap">Associado em</th>
                                    <th class="whitespace-nowrap text-center">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="associacao in associacoesFiltradas" :key="associacao.cd_usuario_perfil">
                                    <td class="font-medium">{{ associacao.perfil?.nm_perfil }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ associacao.perfil?.total_telas || 0 }}</span>
                                    </td>
                                    <td>{{ formatarData(associacao.created_at) }}</td>
                                    <td class="text-center">
                                        <div class="flex justify-center items-center">
                                            <button
                                                @click="verDetalhes(associacao)"
                                                class="btn btn-outline-primary w-8 h-8 mr-1"
                                                title="Ver detalhes"
                                            >
                                                <EyeIcon class="w-4 h-4" />
                                            </button>
                                            <button
                                                @click="desassociarPerfil(associacao)"
                                                class="btn btn-outline-danger w-8 h-8"
                                                title="Remover associação"
                                            >
                                                <TrashIcon class="w-4 h-4" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Estado inicial -->
    <div v-else class="intro-y box p-8 mt-5 text-center">
        <UserIcon class="w-16 h-16 mx-auto text-slate-300 mb-4" />
        <h3 class="text-lg font-medium mb-2">Selecione um usuário</h3>
        <p class="text-slate-500">Escolha um usuário acima para gerenciar suas associações com perfis</p>
    </div>

    <!-- Modal Associar Perfil -->
    <Modal :show="modalAssociar" @hidden="fecharModalAssociar">
        <ModalHeader>
            <h2 class="font-medium text-base mr-auto">Associar Perfil ao Usuário</h2>
        </ModalHeader>
        <ModalBody>
            <div class="grid grid-cols-12 gap-4 gap-y-3">
                <div class="col-span-12">
                    <label class="form-label">Usuário</label>
                    <input
                        :value="usuarioAtual?.ds_nome + ' (' + usuarioAtual?.ds_login + ')'"
                        type="text"
                        class="form-control"
                        readonly
                    />
                </div>
                <div class="col-span-12">
                    <label class="form-label">Perfil *</label>
                    <select
                        v-model="formAssociacao.cd_perfil"
                        class="form-select"
                        :class="{ 'border-danger': erros.cd_perfil }"
                    >
                        <option value="">Selecione um perfil</option>
                        <option 
                            v-for="perfil in perfisDisponiveis" 
                            :key="perfil.cd_perfil" 
                            :value="perfil.cd_perfil"
                        >
                            {{ perfil.nm_perfil }}
                        </option>
                    </select>
                    <div v-if="erros.cd_perfil" class="text-danger text-xs mt-1">
                        {{ erros.cd_perfil }}
                    </div>
                </div>
            </div>
        </ModalBody>
        <ModalFooter>
            <button @click="fecharModalAssociar" class="btn btn-outline-secondary w-20 mr-1">
                Cancelar
            </button>
            <button @click="salvarAssociacao" class="btn btn-primary w-20" :disabled="salvando">
                <LoadingIcon v-if="salvando" class="w-4 h-4 mr-2" />
                Associar
            </button>
        </ModalFooter>
    </Modal>

    <!-- Modal Detalhes do Perfil -->
    <Modal :show="modalDetalhes" @hidden="fecharModalDetalhes">
        <ModalHeader>
            <h2 class="font-medium text-base mr-auto">Detalhes do Perfil</h2>
        </ModalHeader>
        <ModalBody>
            <div v-if="perfilDetalhes">
                <div class="mb-4">
                    <h3 class="font-medium text-lg">{{ perfilDetalhes.nm_perfil }}</h3>
                    <p class="text-slate-500">{{ perfilDetalhes.telas?.length || 0 }} tela(s) associada(s)</p>
                </div>
                
                <div v-if="perfilDetalhes.telas && perfilDetalhes.telas.length > 0">
                    <h4 class="font-medium mb-3">Telas e Permissões:</h4>
                    <div class="space-y-3">
                        <div 
                            v-for="tela in perfilDetalhes.telas" 
                            :key="tela.cd_tela"
                            class="border rounded-lg p-3"
                        >
                            <div class="flex justify-between items-start mb-2">
                                <div>
                                    <h5 class="font-medium">{{ tela.tela?.nm_tela }}</h5>
                                    <code class="text-xs bg-slate-100 px-2 py-1 rounded">{{ tela.tela?.ds_rota }}</code>
                                </div>
                            </div>
                            <div class="flex gap-4 text-sm">
                                <span :class="tela.in_visualizar ? 'text-green-600' : 'text-slate-400'">
                                    {{ tela.in_visualizar ? '✓' : '✗' }} Visualizar
                                </span>
                                <span :class="tela.in_inserir ? 'text-green-600' : 'text-slate-400'">
                                    {{ tela.in_inserir ? '✓' : '✗' }} Inserir
                                </span>
                                <span :class="tela.in_alterar ? 'text-green-600' : 'text-slate-400'">
                                    {{ tela.in_alterar ? '✓' : '✗' }} Alterar
                                </span>
                                <span :class="tela.in_excluir ? 'text-green-600' : 'text-slate-400'">
                                    {{ tela.in_excluir ? '✓' : '✗' }} Excluir
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="text-center py-4 text-slate-500">
                    Nenhuma tela associada a este perfil
                </div>
            </div>
        </ModalBody>
        <ModalFooter>
            <button @click="fecharModalDetalhes" class="btn btn-primary w-20">
                Fechar
            </button>
        </ModalFooter>
    </Modal>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import AccessControlService from '@/services/administracao/AccessControlService';

// Estados
const usuarios = ref([]);
const perfis = ref([]);
const associacoes = ref([]);
const usuarioSelecionado = ref('');
const filtro = ref('');
const modalAssociar = ref(false);
const modalDetalhes = ref(false);
const salvando = ref(false);
const perfilDetalhes = ref(null);

// Formulário
const formAssociacao = ref({
    cd_perfil: ''
});

const erros = ref({});

// Computed
const usuarioAtual = computed(() => {
    return usuarios.value.find(u => u.cd_usuario == usuarioSelecionado.value);
});

const associacoesFiltradas = computed(() => {
    if (!filtro.value) return associacoes.value;
    
    const termo = filtro.value.toLowerCase();
    return associacoes.value.filter(assoc =>
        assoc.perfil?.nm_perfil.toLowerCase().includes(termo)
    );
});

const perfisDisponiveis = computed(() => {
    const perfisAssociados = associacoes.value.map(a => a.cd_perfil);
    return perfis.value.filter(perfil => !perfisAssociados.includes(perfil.cd_perfil));
});

// Métodos
async function carregarDados() {
    try {
        const [responseUsuarios, responsePerfis] = await Promise.all([
            AccessControlService.listarUsuarios(),
            AccessControlService.listarPerfis()
        ]);
        
        if (responseUsuarios.statuscode === 200) {
            usuarios.value = responseUsuarios.data;
        }
        
        if (responsePerfis.statuscode === 200) {
            perfis.value = responsePerfis.data;
        }
    } catch (error) {
        console.error('Erro ao carregar dados:', error);
    }
}

async function carregarAssociacoes() {
    if (!usuarioSelecionado.value) {
        associacoes.value = [];
        return;
    }

    try {
        const response = await AccessControlService.listarAssociacoesUsuariosPerfis({ cd_usuario: usuarioSelecionado.value });
        
        if (response.statuscode === 200) {
            associacoes.value = response.data;
        }
    } catch (error) {
        console.error('Erro ao carregar associações:', error);
    }
}

function abrirModalAssociar() {
    formAssociacao.value = { cd_perfil: '' };
    erros.value = {};
    modalAssociar.value = true;
}

function fecharModalAssociar() {
    modalAssociar.value = false;
    formAssociacao.value = { cd_perfil: '' };
    erros.value = {};
}

async function salvarAssociacao() {
    try {
        salvando.value = true;
        erros.value = {};

        if (!formAssociacao.value.cd_perfil) {
            erros.value.cd_perfil = 'Selecione um perfil';
            return;
        }

        const dados = {
            cd_usuario: usuarioSelecionado.value,
            cd_perfil: formAssociacao.value.cd_perfil
        };

        const response = await AccessControlService.associarUsuarioPerfil(dados);

        if (response.statuscode === 200) {
            await carregarAssociacoes();
            fecharModalAssociar();
        } else {
            console.error('Erro ao salvar associação:', response.message);
        }
    } catch (error) {
        console.error('Erro ao salvar associação:', error);
    } finally {
        salvando.value = false;
    }
}

async function desassociarPerfil(associacao) {
    if (!confirm(`Tem certeza que deseja remover a associação com o perfil "${associacao.perfil?.nm_perfil}"?`)) {
        return;
    }

    try {
        const response = await AccessControlService.desassociarUsuarioPerfil(
            associacao.cd_usuario,
            associacao.cd_perfil
        );

        if (response.statuscode === 200) {
            await carregarAssociacoes();
        } else {
            console.error('Erro ao desassociar perfil:', response.message);
        }
    } catch (error) {
        console.error('Erro ao desassociar perfil:', error);
    }
}

async function verDetalhes(associacao) {
    try {
        const response = await AccessControlService.buscarPerfilPorId(associacao.cd_perfil);
        
        if (response.statuscode === 200) {
            perfilDetalhes.value = response.data;
            modalDetalhes.value = true;
        }
    } catch (error) {
        console.error('Erro ao carregar detalhes do perfil:', error);
    }
}

function fecharModalDetalhes() {
    modalDetalhes.value = false;
    perfilDetalhes.value = null;
}

function formatarData(data) {
    if (!data) return '-';
    return new Date(data).toLocaleDateString('pt-BR');
}

// Lifecycle
onMounted(() => {
    carregarDados();
});
</script>
