import { Request } from 'express';
import { IRetorno, erroInterno, sucesso, conflito, dadosNaoEncontrados, parametrosInvalidos } from '../../interfaces/IRetorno';
import { UsuariosPerfisDB } from '../../data/perfis/UsuariosPerfisDB';
import { PerfisDB } from '../../data/perfis/PerfisDB';
import { UsuarioDB } from '../../data/usuarios/UsuarioDB';

export class UsuariosPerfisModel {
  async associar(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_usuario) {
        return parametrosInvalidos(['O campo "cd_usuario" é obrigatório']);
      }

      if (!req.body.cd_perfil) {
        return parametrosInvalidos(['O campo "cd_perfil" é obrigatório']);
      }

      // Verificar se o usuário existe
      const reqUsuario = { query: { cd_usuario: req.body.cd_usuario } } as unknown as Request;
      const usuarioExistente = await UsuarioDB.listaUsuarios(reqUsuario);
      
      if (usuarioExistente.statuscode !== 200 || usuarioExistente.data.length === 0) {
        return dadosNaoEncontrados('Usuário não encontrado');
      }

      // Verificar se o perfil existe
      req.params = { cd_perfil: req.body.cd_perfil };
      const perfilExistente = await PerfisDB.buscarPorId(req);
      
      if (perfilExistente.statuscode !== 200 || perfilExistente.data.length === 0) {
        return dadosNaoEncontrados('Perfil não encontrado');
      }

      // Verificar se a associação já existe
      const associacaoExistente = await UsuariosPerfisDB.verificarExistencia(req.body.cd_usuario, req.body.cd_perfil);
      
      if (associacaoExistente.statuscode === 200 && associacaoExistente.data.length > 0) {
        return conflito('Este usuário já possui este perfil');
      }

      // Criar a associação
      const resultado = await UsuariosPerfisDB.incluir(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Usuário associado ao perfil com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async desassociar(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_usuario) {
        return parametrosInvalidos(['O campo "cd_usuario" é obrigatório']);
      }

      if (!req.body.cd_perfil) {
        return parametrosInvalidos(['O campo "cd_perfil" é obrigatório']);
      }

      // Verificar se a associação existe
      const associacaoExistente = await UsuariosPerfisDB.verificarExistencia(req.body.cd_usuario, req.body.cd_perfil);
      
      if (associacaoExistente.statuscode !== 200 || associacaoExistente.data.length === 0) {
        return dadosNaoEncontrados('Associação não encontrada');
      }

      // Remover a associação
      const resultado = await UsuariosPerfisDB.removerPorUsuarioEPerfil(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Usuário desassociado do perfil com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listar(req: Request): Promise<IRetorno> {
    try {
      const resultado = await UsuariosPerfisDB.listar(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Associações usuário-perfil listadas com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async buscarPorId(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.params.id) {
        return parametrosInvalidos(['O parâmetro "id" é obrigatório']);
      }

      const resultado = await UsuariosPerfisDB.buscarPorId(req);
      
      if (resultado.statuscode === 200) {
        if (resultado.data.length === 0) {
          return dadosNaoEncontrados('Associação não encontrada');
        }
        return sucesso('Associação encontrada', resultado.data[0]);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listarPerfisPorUsuario(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.params.cd_usuario) {
        return parametrosInvalidos(['O parâmetro "cd_usuario" é obrigatório']);
      }

      const resultado = await UsuariosPerfisDB.listarPerfisPorUsuario(parseInt(req.params.cd_usuario));
      
      if (resultado.statuscode === 200) {
        return sucesso('Perfis do usuário listados com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listarUsuariosSemPerfil(req: Request): Promise<IRetorno> {
    try {
      const resultado = await UsuariosPerfisDB.listarUsuariosSemPerfil(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Usuários sem perfil listados com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async associarMultiplosPerfis(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_usuario) {
        return parametrosInvalidos(['O campo "cd_usuario" é obrigatório']);
      }

      if (!req.body.perfis || !Array.isArray(req.body.perfis) || req.body.perfis.length === 0) {
        return parametrosInvalidos(['O campo "perfis" deve ser um array com pelo menos um perfil']);
      }

      // Verificar se o usuário existe
      const reqUsuario = { query: { cd_usuario: req.body.cd_usuario } } as unknown as Request;
      const usuarioExistente = await UsuarioDB.listaUsuarios(reqUsuario);
      
      if (usuarioExistente.statuscode !== 200 || usuarioExistente.data.length === 0) {
        return dadosNaoEncontrados('Usuário não encontrado');
      }

      const resultados = [];
      const erros = [];

      // Processar cada perfil
      for (const cd_perfil of req.body.perfis) {
        try {
          // Verificar se o perfil existe
          req.params = { cd_perfil: cd_perfil };
          const perfilExistente = await PerfisDB.buscarPorId(req);
          
          if (perfilExistente.statuscode !== 200 || perfilExistente.data.length === 0) {
            erros.push(`Perfil ${cd_perfil} não encontrado`);
            continue;
          }

          // Verificar se a associação já existe
          const associacaoExistente = await UsuariosPerfisDB.verificarExistencia(req.body.cd_usuario, cd_perfil);
          
          if (associacaoExistente.statuscode === 200 && associacaoExistente.data.length > 0) {
            erros.push(`Usuário já possui o perfil ${cd_perfil}`);
            continue;
          }

          // Criar a associação
          const dadosAssociacao = {
            cd_usuario: req.body.cd_usuario,
            cd_perfil: cd_perfil
          };

          req.body = dadosAssociacao;
          const resultado = await UsuariosPerfisDB.incluir(req);
          
          if (resultado.statuscode === 200) {
            resultados.push(resultado.data[0]);
          } else {
            erros.push(`Erro ao associar perfil ${cd_perfil}: ${resultado.message}`);
          }
        } catch (error: any) {
          erros.push(`Erro ao processar perfil ${cd_perfil}: ${error.message}`);
        }
      }

      if (resultados.length === 0 && erros.length > 0) {
        return erroInterno({ message: erros.join('; ') });
      }

      const mensagem = `${resultados.length} associações criadas com sucesso` +
                      (erros.length > 0 ? `. Erros: ${erros.join('; ')}` : '');

      return sucesso({ associacoes_criadas: resultados, erros }, mensagem);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async substituirPerfis(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_usuario) {
        return parametrosInvalidos(['O campo "cd_usuario" é obrigatório']);
      }

      if (!req.body.perfis || !Array.isArray(req.body.perfis)) {
        return parametrosInvalidos(['O campo "perfis" deve ser um array']);
      }

      // Verificar se o usuário existe
      const reqUsuario = { query: { cd_usuario: req.body.cd_usuario } } as unknown as Request;
      const usuarioExistente = await UsuarioDB.listaUsuarios(reqUsuario);
      
      if (usuarioExistente.statuscode !== 200 || usuarioExistente.data.length === 0) {
        return dadosNaoEncontrados('Usuário não encontrado');
      }

      // Primeiro, remover todas as associações existentes do usuário
      const reqListar = { query: { cd_usuario: req.body.cd_usuario } } as unknown as Request;
      const associacoesExistentes = await UsuariosPerfisDB.listar(reqListar);
      
      if (associacoesExistentes.statuscode === 200) {
        for (const associacao of associacoesExistentes.data) {
          const reqRemover = { 
            body: { 
              cd_usuario: req.body.cd_usuario, 
              cd_perfil: associacao.cd_perfil 
            } 
          } as Request;
          await UsuariosPerfisDB.removerPorUsuarioEPerfil(reqRemover);
        }
      }

      // Se não há perfis para associar, retornar sucesso
      if (req.body.perfis.length === 0) {
        return sucesso([], 'Todos os perfis do usuário foram removidos');
      }

      // Associar os novos perfis
      const resultados = [];
      const erros = [];

      for (const cd_perfil of req.body.perfis) {
        try {
          // Verificar se o perfil existe
          req.params = { cd_perfil: cd_perfil };
          const perfilExistente = await PerfisDB.buscarPorId(req);
          
          if (perfilExistente.statuscode !== 200 || perfilExistente.data.length === 0) {
            erros.push(`Perfil ${cd_perfil} não encontrado`);
            continue;
          }

          // Criar a associação
          const dadosAssociacao = {
            cd_usuario: req.body.cd_usuario,
            cd_perfil: cd_perfil
          };

          req.body = dadosAssociacao;
          const resultado = await UsuariosPerfisDB.incluir(req);
          
          if (resultado.statuscode === 200) {
            resultados.push(resultado.data[0]);
          } else {
            erros.push(`Erro ao associar perfil ${cd_perfil}: ${resultado.message}`);
          }
        } catch (error: any) {
          erros.push(`Erro ao processar perfil ${cd_perfil}: ${error.message}`);
        }
      }

      const mensagem = `Perfis do usuário atualizados. ${resultados.length} associações criadas` +
                      (erros.length > 0 ? `. Erros: ${erros.join('; ')}` : '');

      return sucesso({ associacoes_criadas: resultados, erros }, mensagem);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
