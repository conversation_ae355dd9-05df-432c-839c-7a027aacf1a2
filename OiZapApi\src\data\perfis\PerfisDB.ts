require('dotenv').config();
import { Request } from 'express';
import { IRetorno, erroInterno } from '../../interfaces/IRetorno';
import { PostgreSQLServices, OperationObject } from '../../services/PostgreSQLServices';

export class PerfisDB {
  static async incluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['perfis'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async alterar(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['perfis'],
        chaves: { cd_perfil: req.body.cd_perfil },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async remover(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['perfis'],
        chaves: { cd_perfil: req.body.cd_perfil },
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listar(req: Request): Promise<IRetorno> {
    try {
      let sql = `
        SELECT 
          p.cd_perfil,
          p.nm_perfil,
          p.created_at,
          p.updated_at,
          COUNT(up.cd_usuario) as total_usuarios,
          COUNT(pt.cd_tela) as total_telas
        FROM perfis p
        LEFT JOIN usuarios_perfis up ON p.cd_perfil = up.cd_perfil
        LEFT JOIN perfis_telas pt ON p.cd_perfil = pt.cd_perfil
        WHERE 1=1
      `;

      if (req.query.cd_perfil) {
        sql += ` AND p.cd_perfil = '${req.query.cd_perfil}'`;
      }
      if (req.query.nm_perfil) {
        sql += ` AND LOWER(p.nm_perfil) LIKE LOWER('%${req.query.nm_perfil}%')`;
      }

      sql += ` 
        GROUP BY p.cd_perfil, p.nm_perfil, p.created_at, p.updated_at
        ORDER BY p.nm_perfil
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async buscarPorId(req: Request): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          p.cd_perfil,
          p.nm_perfil,
          p.created_at,
          p.updated_at
        FROM perfis p
        WHERE p.cd_perfil = '${req.params.cd_perfil}'
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listarUsuariosPorPerfil(req: Request): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          u.cd_usuario,
          u.ds_nome,
          u.ds_login,
          u.ds_email,
          up.created_at as data_associacao
        FROM usuarios_perfis up
        JOIN adm_usuarios u ON up.cd_usuario = u.cd_usuario
        WHERE up.cd_perfil = '${req.params.cd_perfil}'
        ORDER BY u.ds_nome
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listarTelasPorPerfil(req: Request): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          pt.nr_perfil_tela,
          pt.cd_tela,
          t.nm_tela,
          t.ds_rota,
          pt.in_visualizar,
          pt.in_inserir,
          pt.in_alterar,
          pt.in_excluir,
          pt.created_at
        FROM perfis_telas pt
        JOIN telas t ON pt.cd_tela = t.cd_tela
        WHERE pt.cd_perfil = '${req.params.cd_perfil}'
        ORDER BY t.nm_tela
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
