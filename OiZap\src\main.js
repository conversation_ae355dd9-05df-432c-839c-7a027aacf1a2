import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router/index.js';
import globalComponents from './global-components';
import utils from './utils';
import './assets/css/app.css';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import 'vue3-timepicker/dist/VueTimepicker.css';
import { AccessControlMiddleware } from './middleware/accessControl';

const pinia = createPinia();

pinia.use(piniaPluginPersistedstate);

// Definir o logger globalmente no Vue

const app = createApp(App);

globalComponents(app);
utils(app);

// Registrar plugin de controle de acesso
app.use(AccessControlMiddleware.criarPlugin());

// Registrar o listener global para beforeinstallprompt
window.addEventListener('beforeinstallprompt', (e) => {
    //console.log('Evento beforeinstallprompt disparado globalmente!');
    e.preventDefault();
    // Armazenar o evento em uma variável global ou em um store
    window.deferredPrompt = e;
});

// Registrar o listener para appinstalled
window.addEventListener('appinstalled', () => {
    // console.log('App foi instalado com sucesso (global)');
    window.deferredPrompt = null;
});

app.use(router).use(pinia);

app.mount('#app');
