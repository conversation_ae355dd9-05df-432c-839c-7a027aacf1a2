import { IRetorno, sucesso, erroInterno } from '../../interfaces/IRetorno';
import { PostgreSQLServices } from '../../services/PostgreSQLServices';

export class MenuModel {
  /**
   * Obtém o menu baseado nos perfis e permissões do usuário
   */
  async obterMenuPorUsuario(cd_usuario: number): Promise<IRetorno> {
    try {
      console.log('🔍 MenuModel: Obtendo menu para usuário', cd_usuario);

      // Buscar perfis do usuário
      const perfisQuery = `
        SELECT DISTINCT
          p.cd_perfil,
          p.nm_perfil
        FROM usuarios_perfis up
        JOIN perfis p ON up.cd_perfil = p.cd_perfil
        WHERE up.cd_usuario = ${cd_usuario}
      `;

      const perfisResult = await new PostgreSQLServices().query(perfisQuery);

      if (perfisResult.statuscode !== 200 || !perfisResult.data?.length) {
        console.log('❌ Nenhum perfil encontrado para o usuário');
        return {
          statuscode: 200,
          message: 'Nenhum perfil encontrado para o usuário',
          data: { menu: [], telasPermitidas: [], perfis: [], modulos: [] }
        };
      }

      console.log('📋 Perfis encontrados:', perfisResult.data);

      // Para teste, vou retornar dados mockados para o menu administrativo
      const telasAdminMockadas = [
        {
          cd_tela: 1,
          nm_tela: 'Gestão de Perfis',
          ds_rota: '/oizap/perfis',
          ds_descricao: 'Gerenciar perfis de acesso',
          in_visualizar: true,
          in_inserir: true,
          in_alterar: true,
          in_excluir: true
        },
        {
          cd_tela: 2,
          nm_tela: 'Gestão de Telas',
          ds_rota: '/oizap/telas',
          ds_descricao: 'Gerenciar telas do sistema',
          in_visualizar: true,
          in_inserir: true,
          in_alterar: true,
          in_excluir: true
        },
        {
          cd_tela: 3,
          nm_tela: 'Perfis & Telas',
          ds_rota: '/oizap/perfis-telas',
          ds_descricao: 'Associar perfis às telas',
          in_visualizar: true,
          in_inserir: true,
          in_alterar: true,
          in_excluir: true
        },
        {
          cd_tela: 4,
          nm_tela: 'Usuários & Perfis',
          ds_rota: '/oizap/usuarios-perfis',
          ds_descricao: 'Associar usuários aos perfis',
          in_visualizar: true,
          in_inserir: true,
          in_alterar: true,
          in_excluir: true
        }
      ];

      console.log('🎯 Usando telas mockadas para teste:', telasAdminMockadas);

      // Construir menu simples baseado nas telas
      const telasAdmin: any[] = [];

      // Mapear telas administrativas conhecidas
      const telasAdminMap: any = {
        '/oizap/perfis': {
          icon: 'UsersIcon',
          pageName: 'gestao-perfis',
          title: 'Gestão de Perfis',
          desc: 'Gerencie perfis de acesso'
        },
        '/oizap/telas': {
          icon: 'LayoutIcon',
          pageName: 'gestao-telas',
          title: 'Gestão de Telas',
          desc: 'Gerencie telas do sistema'
        },
        '/oizap/perfis-telas': {
          icon: 'LinkIcon',
          pageName: 'gestao-perfis-telas',
          title: 'Perfis & Telas',
          desc: 'Associe perfis às telas'
        },
        '/oizap/usuarios-perfis': {
          icon: 'UserCheckIcon',
          pageName: 'gestao-usuarios-perfis',
          title: 'Usuários & Perfis',
          desc: 'Associe usuários aos perfis'
        }
      };

      // Verificar se há telas administrativas
      telasAdminMockadas.forEach((tela: any) => {
        if (telasAdminMap[tela.ds_rota] && tela.in_visualizar) {
          telasAdmin.push({
            ...telasAdminMap[tela.ds_rota],
            permissoes: {
              in_visualizar: tela.in_visualizar,
              in_inserir: tela.in_inserir,
              in_alterar: tela.in_alterar,
              in_excluir: tela.in_excluir
            }
          });
        }
      });

      // Construir menu
      const menu: any[] = [];
      if (telasAdmin.length > 0) {
        menu.push({
          icon: 'SettingsIcon',
          title: 'Administração',
          subMenu: telasAdmin
        });
      }

      const resultado = {
        perfis: perfisResult.data,
        modulos: [],
        menu: menu,
        telasPermitidas: telasAdminMockadas.map((tela: any) => ({
          tela: {
            cd_tela: tela.cd_tela,
            nm_tela: tela.nm_tela,
            ds_rota: tela.ds_rota,
            ds_descricao: tela.ds_descricao
          },
          permissoes: {
            in_visualizar: tela.in_visualizar,
            in_inserir: tela.in_inserir,
            in_alterar: tela.in_alterar,
            in_excluir: tela.in_excluir
          }
        }))
      };

      console.log('📱 Menu construído:', resultado);

      return {
        statuscode: 200,
        message: 'Menu obtido com sucesso',
        data: resultado
      };
    } catch (error: any) {
      console.error('❌ Erro ao obter menu por usuário:', error);
      return {
        statuscode: 500,
        message: 'Erro interno',
        data: [],
        errors: [error.message]
      };
    }
  }
}
