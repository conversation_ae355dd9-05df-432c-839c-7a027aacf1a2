import { computed, inject, ref } from 'vue';
import { useRouter } from 'vue-router';

/**
 * Composable para gerenciar permissões de acesso
 */
export function usePermissions() {
    const router = useRouter();
    const accessControl = inject('accessControl');

    /**
     * Verifica se tem acesso a uma rota específica
     */
    const hasAccess = (route, permission = 'visualizar') => {
        if (!accessControl) return true;
        return accessControl.temAcesso(route, permission);
    };

    /**
     * Verifica se pode visualizar
     */
    const canView = (route) => hasAccess(route, 'visualizar');

    /**
     * Verifica se pode criar/inserir
     */
    const canCreate = (route) => hasAccess(route, 'inserir');

    /**
     * Verifica se pode editar/alterar
     */
    const canEdit = (route) => hasAccess(route, 'alterar');

    /**
     * Verifica se pode excluir
     */
    const canDelete = (route) => hasAccess(route, 'excluir');

    /**
     * Obtém todas as permissões de uma rota
     */
    const getPermissions = (route) => {
        if (!accessControl) {
            return {
                in_visualizar: true,
                in_inserir: true,
                in_alterar: true,
                in_excluir: true
            };
        }
        return accessControl.obterPermissoes(route);
    };

    /**
     * Verifica acesso e redireciona se necessário
     */
    const checkAccessOrRedirect = (route, permission = 'visualizar') => {
        if (!hasAccess(route, permission)) {
            router.push({
                name: 'access-denied',
                query: { rota: route, permissao: permission }
            });
            return false;
        }
        return true;
    };

    /**
     * Atualiza as permissões do usuário
     */
    const refreshPermissions = async () => {
        if (accessControl) {
            await accessControl.atualizarPermissoes();
        }
    };

    /**
     * Verifica se é administrador (tem acesso às telas de gestão)
     */
    const isAdmin = computed(() => {
        const rotasAdmin = [
            '/oizap/perfis',
            '/oizap/telas',
            '/oizap/perfis-telas',
            '/oizap/usuarios-perfis'
        ];

        return rotasAdmin.some(rota => canView(rota));
    });

    /**
     * Obtém lista de telas que o usuário tem acesso
     */
    const getAccessibleRoutes = () => {
        if (!accessControl) return [];
        return accessControl.telasPermitidas.map(item => item.tela.ds_rota);
    };

    /**
     * Cria um objeto reativo com permissões para uma rota específica
     */
    const createRoutePermissions = (route) => {
        return computed(() => getPermissions(route));
    };

    /**
     * Helper para mostrar/ocultar elementos baseado em permissões
     */
    const showIf = (route, permission = 'visualizar') => {
        return computed(() => hasAccess(route, permission));
    };

    /**
     * Helper para desabilitar elementos baseado em permissões
     */
    const disableIf = (route, permission = 'visualizar') => {
        return computed(() => !hasAccess(route, permission));
    };

    /**
     * Verifica se tem pelo menos uma das permissões especificadas
     */
    const hasAnyPermission = (route, permissions = ['visualizar']) => {
        return permissions.some(permission => hasAccess(route, permission));
    };

    /**
     * Verifica se tem todas as permissões especificadas
     */
    const hasAllPermissions = (route, permissions = ['visualizar']) => {
        return permissions.every(permission => hasAccess(route, permission));
    };

    /**
     * Cria um guard para componentes
     */
    const createPermissionGuard = (route, permission = 'visualizar', redirectOnFail = true) => {
        return () => {
            const access = hasAccess(route, permission);
            
            if (!access && redirectOnFail) {
                router.push({
                    name: 'access-denied',
                    query: { rota: route, permissao: permission }
                });
            }
            
            return access;
        };
    };

    /**
     * Wrapper para executar ações apenas se tiver permissão
     */
    const executeIfAllowed = (route, permission, action, fallback = null) => {
        if (hasAccess(route, permission)) {
            return action();
        } else if (fallback) {
            return fallback();
        }
        return null;
    };

    return {
        // Verificações básicas
        hasAccess,
        canView,
        canCreate,
        canEdit,
        canDelete,
        
        // Utilitários
        getPermissions,
        checkAccessOrRedirect,
        refreshPermissions,
        getAccessibleRoutes,
        
        // Computed
        isAdmin,
        
        // Helpers para templates
        showIf,
        disableIf,
        createRoutePermissions,
        
        // Verificações avançadas
        hasAnyPermission,
        hasAllPermissions,
        
        // Guards e wrappers
        createPermissionGuard,
        executeIfAllowed
    };
}

/**
 * Composable específico para uma rota
 */
export function useRoutePermissions(route) {
    const permissions = usePermissions();
    
    return {
        route,
        canView: computed(() => permissions.canView(route)),
        canCreate: computed(() => permissions.canCreate(route)),
        canEdit: computed(() => permissions.canEdit(route)),
        canDelete: computed(() => permissions.canDelete(route)),
        permissions: computed(() => permissions.getPermissions(route)),
        checkAccess: (permission = 'visualizar') => permissions.checkAccessOrRedirect(route, permission)
    };
}
