<template>
    <div v-if="temPermissao">
        <slot />
    </div>
    <div v-else-if="showFallback" class="permission-denied-fallback">
        <div class="text-center p-4 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
            <LockIcon class="w-8 h-8 mx-auto text-gray-400 mb-2" />
            <p class="text-sm text-gray-600">
                {{ fallbackMessage || 'Você não possui permissão para visualizar este conteúdo.' }}
            </p>
            <button 
                v-if="showRequestButton"
                @click="solicitarAcesso"
                class="btn btn-outline-primary btn-sm mt-2"
            >
                Solicitar Acesso
            </button>
        </div>
    </div>
</template>

<script setup>
import { computed, inject } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const accessControl = inject('accessControl');

const props = defineProps({
    // Rota para verificar permissão
    route: {
        type: String,
        required: true
    },
    // Tipo de permissão (visualizar, inserir, alterar, excluir)
    permission: {
        type: String,
        default: 'visualizar',
        validator: (value) => ['visualizar', 'inserir', 'alterar', 'excluir'].includes(value)
    },
    // Se deve mostrar fallback quando não tem permissão
    showFallback: {
        type: Boolean,
        default: false
    },
    // Mensagem personalizada para o fallback
    fallbackMessage: {
        type: String,
        default: ''
    },
    // Se deve mostrar botão para solicitar acesso
    showRequestButton: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['access-denied', 'request-access']);

// Computed
const temPermissao = computed(() => {
    if (!accessControl) {
        // Se não há controle de acesso configurado, permitir acesso
        return true;
    }

    const hasAccess = accessControl.temAcesso(props.route, props.permission);
    
    if (!hasAccess) {
        emit('access-denied', {
            route: props.route,
            permission: props.permission
        });
    }
    
    return hasAccess;
});

// Métodos
function solicitarAcesso() {
    emit('request-access', {
        route: props.route,
        permission: props.permission
    });
    
    // Redirecionar para página de acesso negado com informações
    router.push({
        name: 'access-denied',
        query: {
            rota: props.route,
            permissao: props.permission
        }
    });
}
</script>

<style scoped>
.permission-denied-fallback {
    user-select: none;
}
</style>
