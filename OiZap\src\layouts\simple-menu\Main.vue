<template>
    <ShowLoading ref="loading" />
    <ShowNotifications ref="showNotifications" />
    <div class="mt-[4.5rem] md:mt-0" :class="[darkMode ? '' : 'oizap-gradient']">
        <!-- <DarkModeSwitcher /> -->
        <!-- <MainColorSwitcher /> -->
        <MobileMenu
            :formData="formData"
            :privilegio="privilegio"
            :aliasName="aliasName"
            @click:abreListaEstabelecimento="abreListaEstabelecimento"
            @click:abreListaUsuarios="abreListaUsuarios"
            @click:exibirModaSenha="exibirModaSenha = true"
            @click:irParalogin="irParalogin"
            @click:atualizarDados="atualizarDados"
        />
        <div class="flex md:mt-0">
            <!-- BEGIN: Simple Menu -->
            <nav class="side-nav side-nav--simple flex flex-col justify-between pt-2">
                <!-- BEGIN: Logo :to="{ name: 'Home' }" 
      <router-link class="intro-x flex items-center"> -->

                <!-- BEGIN: Account Menu -->
                <Dropdown class="ml-2 mt-1 w-8 h-8">
                    <DropdownToggle
                        tag="div"
                        role="button"
                        class="w-8 h-8 rounded-full overflow-hidden shadow-lg image-fit zoom-in bg-white"
                    >
                        <div class="font-medium text-green-800 flex h-full items-center justify-center">
                            {{ aliasName }}
                        </div>
                    </DropdownToggle>
                    <DropdownMenu class="w-56">
                        <DropdownContent class="bg-primary text-white" :class="[darkMode ? '' : 'oizap-gradient']">
                            <DropdownHeader tag="div" class="!font-normal">
                                <div class="font-medium">{{ formData.name }}</div>
                            </DropdownHeader>
                            <DropdownDivider class="border-white/[0.08]" />

                            <DropdownItem
                                v-show="privilegio != 'Usuário'"
                                class="dropdown-item hover:bg-white/5"
                                @click="abreListaEstabelecimento"
                            >
                                <ContactIcon class="w-4 h-4 mr-2" />Meus Dados
                            </DropdownItem>
                            <DropdownItem
                                class="dropdown-item hover:bg-white/5"
                                @click="abreListaUsuarios"
                                v-show="privilegio != 'Usuário'"
                            >
                                <UserIcon class="w-4 h-4 mr-2" />Usuários
                            </DropdownItem>
                            <DropdownItem class="dropdown-item hover:bg-white/5" @click="exibirModaSenha = true">
                                <KeyIcon class="w-4 h-4 mr-2" />Trocar senha
                            </DropdownItem>
                            <DropdownDivider class="border-white/[0.08]" />
                            <DropdownItem
                                class="dropdown-item hover:bg-white/5"
                                @click="atualizarDados()"
                                v-show="modulos.in_chat_pedidos"
                            >
                                <DatabaseBackupIcon class="w-4 h-4 mr-2" /> Atualizar Dados
                            </DropdownItem>
                            <DropdownDivider class="border-white/[0.08]" />
                            <DropdownItem
                                class="dropdown-item hover:bg-white/5"
                                v-if="showInstallPrompt"
                                @click="installApp"
                            >
                                <MonitorDownIcon class="w-4 h-4 mr-2" /> Instalar o OiZap
                            </DropdownItem>
                            <DropdownDivider class="border-white/[0.08]" />
                            <DropdownItem class="dropdown-item hover:bg-white/5" @click="irParalogin()">
                                <ToggleRightIcon class="w-4 h-4 mr-2" /> Sair
                            </DropdownItem>
                            <!-- <DropdownDivider class="border-white/[0.08]" />
                <DropdownItem class="dropdown-item hover:bg-white/5">
                  <BookUp2Icon class="w-4 h-4 mr-2" /> {{ versaoOiZap }}
                </DropdownItem>
                -->
                        </DropdownContent>
                    </DropdownMenu>
                </Dropdown>
                <!-- END: Account Menu -->
                <!-- </router-link>

        
      END: Logo -->

                <div class="side-nav__devider my-2" style="width: 150% !important"></div>
                <ul class="flex-1 ml-1">
                    <!-- BEGIN: First Child -->
                    <template v-for="(menu, menuKey) in formattedMenu">
                        <li v-if="menu == 'devider'" :key="menu + menuKey" class="side-nav__devider my-6"></li>
                        <li v-else :key="'menu-' + menuKey">
                            <Tippy
                                tag="a"
                                :content="menu.title"
                                :options="{
                                    placement: 'left',
                                }"
                                :href="menu.subMenu ? 'javascript:;' : router.resolve({ name: menu.pageName }).path"
                                class="side-menu"
                                :class="{
                                    'side-menu--active': menu.active,
                                    'side-menu--open': menu.activeDropdown,
                                }"
                                @click="linkTo(menu, router, $event)"
                            >
                                <div class="side-menu__icon">
                                    <component :is="menu.icon" />
                                </div>
                                <div class="side-menu__title">
                                    {{ menu.title }}
                                    <ChevronDownIcon
                                        v-if="$h.isset(menu.subMenu)"
                                        class="side-menu__sub-icon"
                                        :class="{ 'transform rotate-180': menu.activeDropdown }"
                                    />
                                </div>
                            </Tippy>
                            <!-- BEGIN: Second Child -->
                            <transition @enter="enter" @leave="leave">
                                <ul v-if="$h.isset(menu.subMenu) && menu.activeDropdown">
                                    <li v-for="(subMenu, subMenuKey) in menu.subMenu" :key="subMenuKey">
                                        <Tippy
                                            tag="a"
                                            :content="subMenu.title"
                                            :options="{
                                                placement: 'left',
                                            }"
                                            :href="
                                                subMenu.subMenu
                                                    ? 'javascript:;'
                                                    : router.resolve({ name: subMenu.pageName }).path
                                            "
                                            class="side-menu"
                                            :class="{ 'side-menu--active': subMenu.active }"
                                            @click="linkTo(subMenu, router, $event)"
                                        >
                                            <div class="side-menu__icon">
                                                <ActivityIcon />
                                            </div>
                                            <div class="side-menu__title">
                                                {{ subMenu.title }}
                                                <ChevronDownIcon
                                                    v-if="$h.isset(subMenu.subMenu)"
                                                    class="side-menu__sub-icon"
                                                    :class="{
                                                        'transform rotate-180': subMenu.activeDropdown,
                                                    }"
                                                />
                                            </div>
                                        </Tippy>
                                        <!-- BEGIN: Third Child -->
                                        <transition @enter="enter" @leave="leave">
                                            <ul v-if="$h.isset(subMenu.subMenu) && subMenu.activeDropdown">
                                                <li
                                                    v-for="(lastSubMenu, lastSubMenuKey) in subMenu.subMenu"
                                                    :key="lastSubMenuKey"
                                                >
                                                    <Tippy
                                                        tag="a"
                                                        :content="lastSubMenu.title"
                                                        :options="{
                                                            placement: 'left',
                                                        }"
                                                        :href="
                                                            lastSubMenu.subMenu
                                                                ? 'javascript:;'
                                                                : router.resolve({ name: lastSubMenu.pageName }).path
                                                        "
                                                        class="side-menu"
                                                        :class="{ 'side-menu--active': lastSubMenu.active }"
                                                        @click="linkTo(lastSubMenu, router, $event)"
                                                    >
                                                        <div class="side-menu__icon">
                                                            <ZapIcon />
                                                        </div>
                                                        <div class="side-menu__title">
                                                            {{ lastSubMenu.title }}
                                                        </div>
                                                    </Tippy>
                                                </li>
                                            </ul>
                                        </transition>
                                        <!-- END: Third Child -->
                                    </li>
                                </ul>
                            </transition>
                            <!-- END: Second Child -->
                        </li>
                    </template>
                    <!-- END: First Child -->
                </ul>
                <!--
                <div class="ml-3 mb-2 cursor-pointer">
                    <Tippy content="Instalar OiZap" class="tooltip w-6 h-6" v-if="showInstallPrompt">
                        <DownloadIcon
                            class="w-6 h-6 text-white"
                            style="stroke-width: 2"
                            v-if="showInstallPrompt"
                            @click="installApp"
                        />
                    </Tippy>
                </div>
                -->
                <!--
                <div class="form-check ml-1 mb-1">
                 
                              <Tippy content="Mudar estilo OiZap" class="tooltip w-6 h-6 ">
                        <label class="custom-switch">
                             <input  v-model="darkMode"  type="checkbox"  class="form-check-input" 
                            :value="darkMode"  :checked="darkMode"  @change="switchMode($event)"  /> 
                            <span class="switch-icon">
                                 <SunIcon v-if="!darkMode" class="icon" /> <MoonIcon v-else class="icon" /> 
                            </span>
                            
                        </label>
                          </Tippy>
                      
                
                </div>
  -->
                <button @click="switchMode" class="ml-4 mb-2 rounded-ful transition-colors">
                    <SunIcon
                        v-if="!darkMode"
                        class="w-5 h-5 rounded-ful text-white hover:text-gray-900 dark:text-gray-300"
                    />
                    <MoonIcon
                        v-else
                        class="w-5 h-5 rounded-ful text-white hover:dark:text-yellow-600 dark:text-gray-300"
                    />
                </button>

                <!-- Rodapé com a versão -->
                <div class="flex items-center cursor-pointer" @click="home()">
                    <!-- <img v-if="tpAmbiente == 'production'" alt="oizap" src="@/assets/images/logo_oizap.png" /> 
                    <img v-else-if="tpAmbiente == 'dev'" alt="oizap" src="@/assets/images/oizap_dev.png" /> 
                    <img v-else-if="tpAmbiente == 'sandbox'" alt="oizap" src="@/assets/images/oizap_sandbox.png" />-->
                    <img v-if="!darkMode" alt="oizap" class="ml-1.5" src="@/assets/images/logo_oizap_branco.png" />
                    <img v-else alt="oizap" class="ml-1.5" src="@/assets/images/logo.png" />
                </div>

                <div class="side-nav__footer p-1 text-center text-white ml-1">
                    {{ versaoOiZap }}
                </div>
                <div class="side-nav__footer text-center font-bold text-yellow-300" v-show="tpAmbiente == 'dev'">
                    DEV
                </div>
                <div class="side-nav__footer text-center font-bold text-orange-400" v-show="tpAmbiente == 'sandbox'">
                    SANDBOX
                </div>
            </nav>
            <!-- END: Simple Menu -->
            <!-- BEGIN: Content -->
            <div class="content">
                <!-- <TopBar /> -->
                <notificacaoAtualizacao />
                <router-view />
            </div>
            <!-- END: Content -->
        </div>
    </div>

    <!-- BEGIN: Reseta Senha -->
    <Modal :show="exibirModaSenha" @hidden="exibirModaSenha = false">
        <ModalHeader>
            <h2 class="font-medium text-base mr-auto">Altera Senha</h2>
        </ModalHeader>
        <ModalBody class="grid grid-cols-12 gap-4 gap-y-3">
            <div class="col-span-12 sm:col-span-6">
                <label for="modal-form-1" class="form-label">Senha Atual</label>
                <input
                    id="modal-form-senhaatual"
                    v-model.trim="validate.ds_senhaatual.$model"
                    type="password"
                    name="ds_senhaatual"
                    class="form-control col-span-2"
                    :class="{ 'border-danger': validate.ds_senhaatual.$error }"
                />
                <template v-if="validate.ds_senhaatual.$error">
                    <div v-for="(error, index) in validate.ds_senhaatual.$errors" :key="index" class="text-danger mt-2">
                        <div :class="{ error: validate.ds_senhaatual.$error }">Senha Atual obrigatória</div>
                    </div>
                </template>
            </div>
            <div class="col-span-12 sm:col-span-6"></div>
            <div class="col-span-12 sm:col-span-6">
                <label for="modal-form-3" class="form-label">Nova Senha</label>
                <input
                    id="modal-form-senhanova"
                    v-model.trim="validate.ds_senhanova.$model"
                    type="password"
                    name="ds_senhanova"
                    class="form-control col-span-2"
                    :class="{ 'border-danger': validate.ds_senhanova.$error }"
                />
                <template v-if="validate.ds_senhanova.$error">
                    <div v-for="(error, index) in validate.ds_senhanova.$errors" :key="index" class="text-danger mt-2">
                        <div :class="{ error: validate.ds_senhanova.$error }">Senha Nova obrigatória</div>
                    </div>
                </template>
            </div>
            <div class="col-span-12 sm:col-span-6">
                <label for="modal-form-4" class="form-label">Repete</label>
                <input
                    id="modal-form-senhacompara"
                    v-model.trim="validate.ds_senhacompara.$model"
                    type="password"
                    name="ds_senhacompara"
                    class="form-control col-span-2"
                    :class="{ 'border-danger': validate.ds_senhacompara.$error }"
                />
                <template v-if="validate.ds_senhacompara.$error">
                    <div
                        v-for="(error, index) in validate.ds_senhacompara.$errors"
                        :key="index"
                        class="text-danger mt-2"
                    >
                        <div :class="{ error: validate.ds_senhacompara.$error }">Senha de comparação obrigatória</div>
                    </div>
                </template>
            </div>
        </ModalBody>
        <ModalFooter>
            <button type="button" @click="exibirModaSenha = false" class="btn btn-outline-secondary w-20 mr-1">
                Cancelar
            </button>
            <button type="button" class="btn btn-primary w-20" @click="save()">Salvar</button>
        </ModalFooter>
    </Modal>
    <!-- END: Reseta Senha -->
</template>

<script setup>
    import { computed, reactive, onMounted, toRefs, provide, ref, watch } from 'vue';
    import ShowNotifications from '@/components/show-notifications/Main.vue';
    import { useRoute, useRouter } from 'vue-router';
    import { useSimpleMenuStore } from '@/stores/simple-menu';
    import { helper as $h } from '@/utils/helper';
    //import TopBar from '@/components/top-bar/Main.vue';
    import notificacaoAtualizacao from '@/views/administracao/atualizacao/notificacao.vue';
    import MobileMenu from '@/components/mobile-menu/Main.vue';
    //import DarkModeSwitcher from '@/components/dark-mode-switcher/Main.vue';
    //import MainColorSwitcher from '@/components/main-color-switcher/Main.vue';

    import VersaoServices from '@/services/administracao/VersaoServices';
    import EstabelecimentoServices from '@/services/administracao/EstabelecimentoServices';
    import { linkTo, nestedMenu, enter, leave } from '@/layouts/side-menu';
    import dom from '@left4code/tw-starter/dist/js/dom';
    //import hosts from '@/utils/hosts';
    import { usePagina } from '@/stores/pagina';
    import { required, minLength, maxLength } from '@vuelidate/validators';
    import { useVuelidate } from '@vuelidate/core';
    //import notifications from '@/components/show-notifications/Main.vue';
    import UsuariosServices from '@/services/administracao/UsuariosServices';
    //import logger from '../../utils/logger';
    //import { alteraSenha } from '@/services/administracao/usuarios';

    import { useDarkModeStore } from '@/stores/dark-mode';
    const darkModeStore = useDarkModeStore();
    const darkMode = computed(() => darkModeStore.darkMode);

    let exibirModaSenha = ref(false);
    let pagina = usePagina();
    const showNotifications = ref();

    let nomeLoja = ref('');
    let nomeUsuario = ref('');
    let privilegio = ref('');
    const tpAmbiente = ref('');
    let aliasName = ref('');
    let codUsuario = ref();
    const versaoOiZap = ref('');
    const route = useRoute();
    const router = useRouter();
    const formattedMenu = ref([]);
    const simpleMenuStore = useSimpleMenuStore();
    const simpleMenu = computed(() => nestedMenu(simpleMenuStore.menu, route));

    provide('forceActiveMenu', (pageName) => {
        route.forceActiveMenu = pageName;

        formattedMenu.value = $h.toRaw(simpleMenu.value);
    });

    const formData = reactive({
        ds_senhacompara: '',
        ds_senhanova: '',
        ds_senhaatual: '',
    });

    const rules = {
        ds_senhaatual: {
            required,
            minLength: minLength(3),
            maxLength: maxLength(20),
        },
        ds_senhanova: {
            required,
            minLength: minLength(3),
            maxLength: maxLength(20),
        },
        ds_senhacompara: {
            required,
            minLength: minLength(3),
            maxLength: maxLength(20),
        },
    };

    const showInstallPrompt = ref(false);
    const deferredPrompt = ref(null);
    const modulos = reactive({ in_chat_pedidos: false, in_api: false, in_crm: false });

    function irParalogin() {
        if (listaInstancia.value[0]?.telefone != undefined) {
            desconectaRoom(listaInstancia.value[0].telefone);
        }
        localStorage.removeItem('instancia');
        localStorage.removeItem('token');
        router.push({ name: 'Login' });
    }
    const formDataSenha = reactive({
        ds_senhacompara: '',
        ds_senhanova: '',
        ds_senhaatual: '',
    });

    const validate = useVuelidate(rules, toRefs(formData));

    const props = defineProps({
        class: {
            type: String,
        },
    });

    async function save() {
        validate.value.$touch();

        if (validate.value.$invalid) {
            showNotifications.value.showErrorNotification('Campos obrigatórios sem preencher.');
        } else if (formDataSenha.ds_senhanova != formDataSenha.ds_senhacompara) {
            showNotifications.value.showErrorNotification('Nova senha diferente da senha de comparação.');
        } else {
            let codEmpOrigem = await localStorage.getItem('loja');
            const dados = reactive({
                ds_senhaatual: formDataSenha.ds_senhaatual,
                ds_senhanova: formDataSenha.ds_senhanova,
                cd_usuariocad: codUsuario.value,
                cd_usuario: codUsuario,
                cd_empresaorigem: codEmpOrigem,
            });

            //console.log("dados",dados);

            let result = await UsuariosServices.alteraSenha(dados);

            //console.log("alteraSenha",message,data);

            if (result.statuscode < 400) {
                showNotifications.value.showSuccessNotification('Senha alterada com sucesso');
                exibirModaSenha.value = false;
            } else {
                showNotifications.value.showErrorNotification(result.message);
            }
        }
    }

    const listaInstancia = ref([
        {
            id: undefined,
            nameinstance: undefined,
            telefone: undefined,
            status: 'Desconectado',
            cor: 'red',
            class_btn: 'btn-danger',
            state_api: undefined,
        },
    ]);
    const loading = ref();

    async function abreListaEstabelecimento() {
        router.push({ name: 'cadEstabelecimento' });
    }

    async function limpaDados() {
        if (formData.cd_estabelecimento == undefined) {
            showNotifications.value.showWarningNotification('Estabelecimento é obrigatório!');
            return;
        }
        loading.value.show();
        let reqObj = {
            cd_estabelecimento: formData.cd_estabelecimento,
        };
        const result = await EstabelecimentoServices.limpaDados(reqObj);
        //console.log('result at line 294 in estabelecimento/cadEstabelecimento.vue:', result);
        if (result.statuscode == 200) {
            //showNotifications.value.showSuccessNotification('Dados limpos com sucesso!');
            router.push({ name: 'listaAtualizaTabelas' });
        } else {
            showNotifications.value.showWarningNotification(result.message);
        }
        loading.value.hide();
    }

    async function atualizarDados() {
        await limpaDados();
    }

    async function abreListaUsuarios() {
        router.push({ name: 'listaUsuarios' });
    }

    const home = () => {
        if (privilegio.value == 'OiZap') {
            localStorage.removeItem('estabelecimentos');
            router.push({ name: 'estabelecimentos' });
        }
    };

    async function verificaVersao() {
        const ambiente = import.meta.env.MODE;

        let nmVersao;
        if (ambiente == 'production') {
            nmVersao = 'OIZAP_OIZAP';
        } else {
            nmVersao = ambiente.toUpperCase() + '_OIZAP';
        }
        const result = await VersaoServices.listar({ nm_versao: nmVersao });
        if (result.statuscode == 200) {
            const resultData = result.data[0];
            versaoOiZap.value = resultData.ds_versao;
            localStorage.setItem('versaoOiZap', resultData.ds_versao);
        }
    }

    function logout() {
        localStorage.removeItem('data');
        localStorage.removeItem('home');
        localStorage.removeItem('codusuario');
        localStorage.removeItem('topMenu');
        localStorage.removeItem('dataExptoken');
        localStorage.removeItem('darkMode');
        localStorage.removeItem('usuario');
        localStorage.removeItem('privilegio');
        localStorage.removeItem('loginStore');
        localStorage.removeItem('token');
        router.push({ name: 'Login' });
    }

    const setDarkModeClass = () => {
        darkMode.value ? dom('html').addClass('dark') : dom('html').removeClass('dark');
    };

    const switchMode = () => {
        darkModeStore.setDarkMode(!darkMode.value);
        setDarkModeClass();
    };

    watch(
        computed(() => route.path),
        () => {
            delete route.forceActiveMenu;
            formattedMenu.value = $h.toRaw(simpleMenu.value);
        }
    );

    // const handleClickOutside = (event) => {
    //     const dropdownElement = document.querySelector('.dropdown-toggle');
    //     if (dropdownElement && !dropdownElement.contains(event.target)) {
    //         isDropdownOpen.value = false;
    //     }
    // };

    // Função para disparar a instalação
    const installApp = async () => {
        //  console.log('Botão de instalação clicado');
        if (deferredPrompt.value) {
            // console.log('Disparando o prompt de instalação...');
            deferredPrompt.value.prompt();
            const { outcome } = await deferredPrompt.value.userChoice;
            if (outcome === 'accepted') {
                // console.log('Usuário aceitou instalar o app');
                showInstallPrompt.value = false;
            } else {
                //  console.log('Usuário recusou instalar o app');
            }
            deferredPrompt.value = null;
            window.deferredPrompt = null;
        } else {
            // console.log('deferredPrompt não está definido. O evento beforeinstallprompt não foi disparado.');
        }
    };

    // Listener para capturar o evento beforeinstallprompt no componente
    window.addEventListener('beforeinstallprompt', (e) => {
        // console.log('Evento beforeinstallprompt disparado no componente!');
        e.preventDefault();
        deferredPrompt.value = e;
        window.deferredPrompt = e;
        showInstallPrompt.value = true;
    });

    // Listener para appinstalled
    window.addEventListener('appinstalled', () => {
        // console.log('App foi instalado com sucesso no componente');
        showInstallPrompt.value = false;
        deferredPrompt.value = null;
        window.deferredPrompt = null;
    });

    onMounted(async () => {
        const respModulos = await EstabelecimentoModulosServices.listaModulosAtivos();
        modulos.in_chat_pedidos = respModulos.in_chat_pedidos;
        modulos.in_api = respModulos.in_api;
        modulos.in_crm = respModulos.in_crm;

        //console.log('onMounted executado');
        if (window.matchMedia('(display-mode: standalone)').matches) {
            // console.log('O app já está instalado e rodando em modo standalone.');
            showInstallPrompt.value = false;
        } else {
            //console.log('O app não está instalado. Verificando deferredPrompt...');
            if (window.deferredPrompt) {
                // console.log('deferredPrompt encontrado globalmente!');
                deferredPrompt.value = window.deferredPrompt;
                showInstallPrompt.value = true;
            } else {
                // console.log('Aguardando o evento beforeinstallprompt...');
            }

            if ('serviceWorker' in navigator) {
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    //console.log('Service Worker já está registrado:', registration);
                    registration.update();
                } else {
                    // console.log('Nenhum Service Worker registrado. Tentando registrar...');
                    navigator.serviceWorker
                        .register('/sw.js')
                        .then((reg) => {
                            //console.log('Service Worker registrado com sucesso:', reg);
                            reg.update();
                        })
                        .catch((err) => console.error('Erro ao registrar o Service Worker:', err));
                }
            } else {
                // console.log('Service Workers não são suportados neste navegador.');
            }
        }

        tpAmbiente.value = import.meta.env.MODE;
        //versaoOiZap.value = import.meta.env.PACKAGE_VERSION;
        await verificaVersao();
        dom('body').removeClass('error-page').removeClass('login').addClass('main');
        formattedMenu.value = $h.toRaw(simpleMenu.value);
        nomeLoja.value = localStorage.getItem('nomeLoja');
        nomeUsuario.value = localStorage.getItem('usuario');
        privilegio.value = localStorage.getItem('privilegio');
        console.log('privilegio.value at line 479 in simple-menu/Main.vue:', privilegio.value);

        const estabelecimentos = localStorage.getItem('estabelecimentos');

        if (estabelecimentos != undefined) {
            const estabelecimentosLiberado = JSON.parse(localStorage.getItem('estabelecimentos'));
            formData.cd_estabelecimento = estabelecimentosLiberado[0].cd_estabelecimento;
        }

        codUsuario.value = localStorage.getItem('codusuario');

        let dataExptoken = localStorage.getItem('dataExptoken');
        if (dataExptoken == null || Date.now() > dataExptoken) {
            let mensagem = `Sessão expirada. Faça login novamente`;
            router.push({ name: 'Login', query: { error: mensagem } });
        }

        if (nomeUsuario.value != undefined) {
            aliasName.value = nomeUsuario.value
                .split(' ')
                .map((word) => word[0].toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');
            const words = aliasName.value.split(' ');
            if (words.length > 1) {
                aliasName.value = words[0].substring(0, 1) + words[1].substring(0, 1);
            } else {
                aliasName.value = words[0].substring(0, 1);
            }

            formData.alias = aliasName.value;
            formData.name = nomeUsuario.value;
        }

        setDarkModeClass();
    });
</script>
<style scoped>
    .custom-gradient-bg {
        background-image: linear-gradient(165deg, #128c7e 0%, #25d366 100%);
    }
    .custom-switch {
        position: relative;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
    }

    /* Esconde o checkbox padrão */
    .form-check-input {
        appearance: none;
        -webkit-appearance: none;
        width: 40px;
        height: 20px;
        background-color: #f6f6f6; /* Verde quando desligado */
        border-radius: 20px !important;
        border-color: #2196f3;
        position: relative;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    /* Círculo do switch */
    .form-check-input::before {
        content: '';
        position: absolute;
        width: 18px;
        height: 18px;
        left: 1px;
        top: 1px;
        background-color: white;
        border-radius: 50%;
        transition: transform 0.3s;
    }

    /* Quando ligado */
    .form-check-input:checked {
        background-color: #2196f3; /* Azul quando ligado */
    }

    .form-check-input:checked::before {
        transform: translateX(20px);
    }

    /* Posiciona o ícone dentro do círculo */
    .switch-icon {
        position: absolute;
        left: 1px; /* Alinha com o círculo */
        top: 1px; /* Alinha com o círculo */
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none; /* Evita interferência no clique */
        transition: transform 0.3s;
    }

    .form-check-input:checked + .switch-icon {
        transform: translateX(20px);
    }

    .icon {
        width: 14px;
        height: 14px;
        color: #333333;
    }
</style>
