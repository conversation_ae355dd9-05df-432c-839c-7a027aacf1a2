<template>
    <div class="intro-y flex flex-col sm:flex-row items-center mt-8">
        <h2 class="text-lg font-medium mr-auto"><PERSON>est<PERSON> de Perfis</h2>
        <div class="w-full sm:w-auto flex mt-4 sm:mt-0">
            <button @click="abrirModalCriar" class="btn btn-primary shadow-md mr-2">
                <PlusIcon class="w-4 h-4 mr-2" />
                Novo Perfil
            </button>
        </div>
    </div>

    <!-- Lista de Perfis -->
    <div class="intro-y grid grid-cols-12 gap-6 mt-5">
        <div class="col-span-12">
            <div class="intro-y box">
                <div class="flex flex-col sm:flex-row items-center p-5 border-b border-slate-200/60">
                    <h2 class="font-medium text-base mr-auto">Lista de Perfis</h2>
                    <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                        <input
                            v-model="filtro"
                            type="text"
                            class="form-control w-full sm:w-64"
                            placeholder="Buscar perfil..."
                        />
                    </div>
                </div>
                <div class="p-5">
                    <div class="overflow-x-auto">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th class="whitespace-nowrap">ID</th>
                                    <th class="whitespace-nowrap">Nome do Perfil</th>
                                    <th class="whitespace-nowrap">Total Usuários</th>
                                    <th class="whitespace-nowrap">Total Telas</th>
                                    <th class="whitespace-nowrap">Criado em</th>
                                    <th class="whitespace-nowrap text-center">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="perfil in perfisFiltrados" :key="perfil.cd_perfil">
                                    <td>{{ perfil.cd_perfil }}</td>
                                    <td class="font-medium">{{ perfil.nm_perfil }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ perfil.total_usuarios || 0 }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ perfil.total_telas || 0 }}</span>
                                    </td>
                                    <td>{{ formatarData(perfil.created_at) }}</td>
                                    <td class="text-center">
                                        <div class="flex justify-center items-center">
                                            <button
                                                @click="editarPerfil(perfil)"
                                                class="btn btn-outline-secondary w-8 h-8 mr-1"
                                                title="Editar"
                                            >
                                                <EditIcon class="w-4 h-4" />
                                            </button>
                                            <button
                                                @click="gerenciarTelas(perfil)"
                                                class="btn btn-outline-primary w-8 h-8 mr-1"
                                                title="Gerenciar Telas"
                                            >
                                                <SettingsIcon class="w-4 h-4" />
                                            </button>
                                            <button
                                                @click="excluirPerfil(perfil)"
                                                class="btn btn-outline-danger w-8 h-8"
                                                title="Excluir"
                                            >
                                                <TrashIcon class="w-4 h-4" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Criar/Editar Perfil -->
    <Modal :show="modalPerfil" @hidden="fecharModal">
        <ModalHeader>
            <h2 class="font-medium text-base mr-auto">
                {{ perfilEditando ? 'Editar Perfil' : 'Novo Perfil' }}
            </h2>
        </ModalHeader>
        <ModalBody>
            <div class="grid grid-cols-12 gap-4 gap-y-3">
                <div class="col-span-12">
                    <label class="form-label">Nome do Perfil *</label>
                    <input
                        v-model="formPerfil.nm_perfil"
                        type="text"
                        class="form-control"
                        placeholder="Digite o nome do perfil"
                        :class="{ 'border-danger': erros.nm_perfil }"
                    />
                    <div v-if="erros.nm_perfil" class="text-danger text-xs mt-1">
                        {{ erros.nm_perfil }}
                    </div>
                </div>
            </div>
        </ModalBody>
        <ModalFooter>
            <button @click="fecharModal" class="btn btn-outline-secondary w-20 mr-1">
                Cancelar
            </button>
            <button @click="salvarPerfil" class="btn btn-primary w-20" :disabled="salvando">
                <LoadingIcon v-if="salvando" class="w-4 h-4 mr-2" />
                {{ perfilEditando ? 'Atualizar' : 'Criar' }}
            </button>
        </ModalFooter>
    </Modal>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import AccessControlService from '@/services/administracao/AccessControlService';

const router = useRouter();

// Estados
const perfis = ref([]);
const filtro = ref('');
const modalPerfil = ref(false);
const perfilEditando = ref(null);
const salvando = ref(false);
const carregando = ref(false);

// Formulário
const formPerfil = ref({
    nm_perfil: ''
});

const erros = ref({});

// Computed
const perfisFiltrados = computed(() => {
    if (!filtro.value) return perfis.value;
    
    return perfis.value.filter(perfil =>
        perfil.nm_perfil.toLowerCase().includes(filtro.value.toLowerCase())
    );
});

// Métodos
async function carregarPerfis() {
    try {
        carregando.value = true;
        const response = await AccessControlService.listarPerfis();
        
        if (response.statuscode === 200) {
            perfis.value = response.data;
        }
    } catch (error) {
        console.error('Erro ao carregar perfis:', error);
    } finally {
        carregando.value = false;
    }
}

function abrirModalCriar() {
    perfilEditando.value = null;
    formPerfil.value = { nm_perfil: '' };
    erros.value = {};
    modalPerfil.value = true;
}

function editarPerfil(perfil) {
    perfilEditando.value = perfil;
    formPerfil.value = { nm_perfil: perfil.nm_perfil };
    erros.value = {};
    modalPerfil.value = true;
}

function fecharModal() {
    modalPerfil.value = false;
    perfilEditando.value = null;
    formPerfil.value = { nm_perfil: '' };
    erros.value = {};
}

async function salvarPerfil() {
    try {
        salvando.value = true;
        erros.value = {};

        // Validações
        if (!formPerfil.value.nm_perfil?.trim()) {
            erros.value.nm_perfil = 'Nome do perfil é obrigatório';
            return;
        }

        if (formPerfil.value.nm_perfil.length < 3) {
            erros.value.nm_perfil = 'Nome deve ter pelo menos 3 caracteres';
            return;
        }

        let response;
        
        if (perfilEditando.value) {
            response = await AccessControlService.atualizarPerfil(
                perfilEditando.value.cd_perfil,
                formPerfil.value
            );
        } else {
            response = await AccessControlService.criarPerfil(formPerfil.value);
        }

        if (response.statuscode === 200) {
            await carregarPerfis();
            fecharModal();
            // Mostrar notificação de sucesso
        } else {
            // Mostrar erro
            console.error('Erro ao salvar perfil:', response.message);
        }
    } catch (error) {
        console.error('Erro ao salvar perfil:', error);
    } finally {
        salvando.value = false;
    }
}

async function excluirPerfil(perfil) {
    if (!confirm(`Tem certeza que deseja excluir o perfil "${perfil.nm_perfil}"?`)) {
        return;
    }

    try {
        const response = await AccessControlService.deletarPerfil(perfil.cd_perfil);
        
        if (response.statuscode === 200) {
            await carregarPerfis();
            // Mostrar notificação de sucesso
        } else {
            // Mostrar erro
            console.error('Erro ao excluir perfil:', response.message);
        }
    } catch (error) {
        console.error('Erro ao excluir perfil:', error);
    }
}

function gerenciarTelas(perfil) {
    router.push({
        name: 'gestao-perfis-telas',
        query: { cd_perfil: perfil.cd_perfil, nm_perfil: perfil.nm_perfil }
    });
}

function formatarData(data) {
    if (!data) return '-';
    return new Date(data).toLocaleDateString('pt-BR');
}

// Lifecycle
onMounted(() => {
    carregarPerfis();
});
</script>
