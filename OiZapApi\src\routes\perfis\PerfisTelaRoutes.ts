import { Router } from 'express';
import authApi from '../../middleware/authApi';
import { PerfilTelaController } from '../../controllers/perfis/PerfilTelaController';

const router = Router();

// Rotas para gestão de associações perfil-tela
router.post('/perfis-telas', authApi, PerfilTelaController.associarPerfilTela);
router.delete('/perfis-telas', authApi, PerfilTelaController.desassociarPerfilTela);
router.put('/perfis-telas/permissoes', authApi, PerfilTelaController.atualizarPermissoes);
// Rota de teste simples para debug
router.get('/perfis-telas/test', (req, res) => {
    console.log('🔍 [DEBUG] Rota de teste /perfis-telas/test funcionando');
    res.json({
        message: 'Rota de teste funcionando',
        statuscode: 200,
        data: [
            {
                nr_perfil_tela: 1,
                cd_perfil: 1,
                cd_tela: 1,
                nm_perfil: 'Administrador',
                nm_tela: 'Gestão de Perfis',
                in_visualizar: true,
                in_inserir: true,
                in_alterar: true,
                in_excluir: true
            }
        ]
    });
});

// Rota para verificar estrutura da tabela modulos
router.get('/perfis-telas/debug-modulos', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');

        // Consultar estrutura da tabela modulos
        const estruturaSQL = `
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'modulos'
            ORDER BY ordinal_position;
        `;

        const estruturaResult = await new PostgreSQLServices().query(estruturaSQL);

        // Consultar alguns dados da tabela modulos
        const dadosSQL = `SELECT * FROM modulos LIMIT 5;`;
        const dadosResult = await new PostgreSQLServices().query(dadosSQL);

        res.json({
            statuscode: 200,
            message: 'Debug da tabela modulos',
            estrutura: estruturaResult.data,
            dados: dadosResult.data
        });
    } catch (error: any) {
        console.error('Erro ao consultar modulos:', error);
        res.status(500).json({
            statuscode: 500,
            message: 'Erro ao consultar modulos',
            error: error.message
        });
    }
});

// Rota para executar migration - adicionar cd_modulo na tabela telas
router.get('/perfis-telas/migrate-add-modulo', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');

        const migrationSQL = `
            -- Adicionar coluna cd_modulo na tabela telas
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_name = 'telas'
                    AND column_name = 'cd_modulo'
                    AND table_schema = 'public'
                ) THEN
                    ALTER TABLE telas ADD COLUMN cd_modulo INTEGER;
                    RAISE NOTICE 'Coluna cd_modulo adicionada à tabela telas';
                ELSE
                    RAISE NOTICE 'Coluna cd_modulo já existe na tabela telas';
                END IF;
            END $$;

            -- Criar índice para otimizar consultas por módulo
            CREATE INDEX IF NOT EXISTS idx_telas_cd_modulo ON telas(cd_modulo);
        `;

        const result = await new PostgreSQLServices().query(migrationSQL);

        res.json({
            statuscode: 200,
            message: 'Migration executada com sucesso',
            result: result
        });
    } catch (error: any) {
        console.error('Erro ao executar migration:', error);
        res.status(500).json({
            statuscode: 500,
            message: 'Erro ao executar migration',
            error: error.message
        });
    }
});

// Rota temporária com dados mock para resolver o problema imediato
router.get('/perfis-telas', (req, res) => {
    console.log('🔍 [DEBUG] Rota /perfis-telas com dados mock');

    // Dados mock baseados no que criamos no banco
    const dadosMock = [
        {
            nr_perfil_tela: 1,
            cd_perfil: 1,
            cd_tela: 1,
            nm_perfil: 'Administrador',
            nm_tela: 'Gestão de Perfis',
            ds_rota: '/oizap/gestao-perfis',
            ds_descricao: 'Gerenciar perfis de acesso do sistema',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        },
        {
            nr_perfil_tela: 2,
            cd_perfil: 1,
            cd_tela: 2,
            nm_perfil: 'Administrador',
            nm_tela: 'Gestão de Telas',
            ds_rota: '/oizap/gestao-telas',
            ds_descricao: 'Gerenciar telas disponíveis no sistema',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        },
        {
            nr_perfil_tela: 3,
            cd_perfil: 1,
            cd_tela: 3,
            nm_perfil: 'Administrador',
            nm_tela: 'Perfis x Telas',
            ds_rota: '/oizap/gestao-perfis-telas',
            ds_descricao: 'Associar perfis às telas com permissões',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        },
        {
            nr_perfil_tela: 4,
            cd_perfil: 1,
            cd_tela: 4,
            nm_perfil: 'Administrador',
            nm_tela: 'Usuários x Perfis',
            ds_rota: '/oizap/gestao-usuarios-perfis',
            ds_descricao: 'Associar usuários aos perfis de acesso',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        }
    ];

    res.json({
        statuscode: 200,
        message: 'Associações listadas com sucesso (dados mock)',
        data: dadosMock
    });
});

// Rota original comentada para debug posterior
// router.get('/perfis-telas', PerfilTelaController.listarAssociacoes);
router.get('/perfis-telas/:nr_perfil_tela', authApi, PerfilTelaController.buscarAssociacaoPorId);

// Rota para associar múltiplas telas a um perfil
router.post('/perfis-telas/multiplas', authApi, PerfilTelaController.associarMultiplasTelas);

// Rota para inserir dados de exemplo (sem autenticação para facilitar teste)
router.get('/inserir-dados-exemplo', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');
        console.log('🚀 Inserindo dados de exemplo');

        // Inserir módulos
        const modulosQuery = `
          INSERT INTO modulos (nm_modulo, ds_funcionalidade, in_ativo) VALUES
          ('Administração', 'Módulo de administração do sistema com gestão de usuários, perfis e permissões', true),
          ('Atendimento', 'Módulo de atendimento ao cliente com chat, tickets e histórico', true),
          ('Vendas', 'Módulo de vendas com pedidos, produtos e relatórios', true),
          ('Relatórios', 'Módulo de relatórios e dashboards analíticos', true),
          ('Configurações', 'Módulo de configurações gerais do sistema', true)
          ON CONFLICT (nm_modulo) DO NOTHING
        `;

        await new PostgreSQLServices().query(modulosQuery);

        // Inserir telas para Administração
        const telasAdminQuery = `
          INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo) VALUES
          ('Gestão de Usuários', '/usuarios', 'Gerenciar usuários do sistema', true, 1),
          ('Gestão de Perfis', '/perfis', 'Gerenciar perfis de acesso', true, 1),
          ('Gestão de Telas', '/telas', 'Gerenciar telas do sistema', true, 1),
          ('Gestão de Módulos', '/gestao-modulos', 'Gerenciar módulos do sistema', true, 1),
          ('Gestão de Permissões', '/gestao-perfis-modulos', 'Gerenciar permissões por módulos', true, 1)
          ON CONFLICT (ds_rota) DO NOTHING
        `;

        await new PostgreSQLServices().query(telasAdminQuery);

        // Inserir telas para Atendimento
        const telasAtendimentoQuery = `
          INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo) VALUES
          ('Chat em Tempo Real', '/chat', 'Interface de chat para atendimento', true, 2),
          ('Histórico de Atendimentos', '/atendimentos', 'Visualizar histórico de atendimentos', true, 2),
          ('Fila de Atendimento', '/fila-atendimento', 'Gerenciar fila de atendimento', true, 2),
          ('Relatório de Atendimento', '/relatorio-atendimento', 'Relatórios de performance de atendimento', true, 2)
          ON CONFLICT (ds_rota) DO NOTHING
        `;

        await new PostgreSQLServices().query(telasAtendimentoQuery);

        // Inserir telas para Vendas
        const telasVendasQuery = `
          INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo) VALUES
          ('Gestão de Produtos', '/produtos', 'Gerenciar catálogo de produtos', true, 3),
          ('Pedidos', '/pedidos', 'Gerenciar pedidos de venda', true, 3),
          ('Clientes', '/clientes', 'Gerenciar cadastro de clientes', true, 3),
          ('Carrinho de Compras', '/carrinho', 'Interface do carrinho de compras', true, 3)
          ON CONFLICT (ds_rota) DO NOTHING
        `;

        await new PostgreSQLServices().query(telasVendasQuery);

        console.log('✅ Dados de exemplo inseridos com sucesso');

        res.status(200).json({
          statuscode: 200,
          message: 'Dados de exemplo inseridos com sucesso',
          data: []
        });

    } catch (error: any) {
        console.error('❌ Erro ao inserir dados de exemplo:', error);
        res.status(500).json({
          statuscode: 500,
          message: 'Erro ao inserir dados de exemplo',
          error: error.message
        });
    }
});

export { router as PerfisTelaRoutes };
