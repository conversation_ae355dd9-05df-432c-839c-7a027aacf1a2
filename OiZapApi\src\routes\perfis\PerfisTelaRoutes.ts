import { Router } from 'express';
import authApi from '../../middleware/authApi';
import { PerfilTelaController } from '../../controllers/perfis/PerfilTelaController';

const router = Router();

// Rotas para gestão de associações perfil-tela
router.post('/oizap/perfis-telas', authApi, PerfilTelaController.associarPerfilTela);
router.delete('/oizap/perfis-telas', authApi, PerfilTelaController.desassociarPerfilTela);
router.put('/oizap/perfis-telas/permissoes', authApi, PerfilTelaController.atualizarPermissoes);
router.get('/oizap/perfis-telas', authApi, PerfilTelaController.listarAssociacoes);
router.get('/oizap/perfis-telas/:nr_perfil_tela', auth<PERSON><PERSON>, PerfilTelaController.buscarAssociacaoPorId);

// Rota para associar múltiplas telas a um perfil
router.post('/oizap/perfis-telas/multiplas', authApi, PerfilTelaController.associarMultiplasTelas);

export { router as PerfisTelaRoutes };
