import { Router } from 'express';
import authApi from '../../middleware/authApi';
import { PerfilTelaController } from '../../controllers/perfis/PerfilTelaController';

const router = Router();

// Rotas para gestão de associações perfil-tela
router.post('/perfis-telas', authApi, PerfilTelaController.associarPerfilTela);
router.delete('/perfis-telas', authApi, PerfilTelaController.desassociarPerfilTela);
router.put('/perfis-telas/permissoes', authApi, PerfilTelaController.atualizarPermissoes);
// Rota de teste simples para debug
router.get('/perfis-telas/test', (req, res) => {
    console.log('🔍 [DEBUG] Rota de teste /perfis-telas/test funcionando');
    res.json({
        message: 'Rota de teste funcionando',
        statuscode: 200,
        data: [
            {
                nr_perfil_tela: 1,
                cd_perfil: 1,
                cd_tela: 1,
                nm_perfil: 'Administrador',
                nm_tela: 'Gestão de Perfis',
                in_visualizar: true,
                in_inserir: true,
                in_alterar: true,
                in_excluir: true
            }
        ]
    });
});

// Rota temporária com dados mock para resolver o problema imediato
router.get('/perfis-telas', (req, res) => {
    console.log('🔍 [DEBUG] Rota /perfis-telas com dados mock');

    // Dados mock baseados no que criamos no banco
    const dadosMock = [
        {
            nr_perfil_tela: 1,
            cd_perfil: 1,
            cd_tela: 1,
            nm_perfil: 'Administrador',
            nm_tela: 'Gestão de Perfis',
            ds_rota: '/oizap/gestao-perfis',
            ds_descricao: 'Gerenciar perfis de acesso do sistema',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        },
        {
            nr_perfil_tela: 2,
            cd_perfil: 1,
            cd_tela: 2,
            nm_perfil: 'Administrador',
            nm_tela: 'Gestão de Telas',
            ds_rota: '/oizap/gestao-telas',
            ds_descricao: 'Gerenciar telas disponíveis no sistema',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        },
        {
            nr_perfil_tela: 3,
            cd_perfil: 1,
            cd_tela: 3,
            nm_perfil: 'Administrador',
            nm_tela: 'Perfis x Telas',
            ds_rota: '/oizap/gestao-perfis-telas',
            ds_descricao: 'Associar perfis às telas com permissões',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        },
        {
            nr_perfil_tela: 4,
            cd_perfil: 1,
            cd_tela: 4,
            nm_perfil: 'Administrador',
            nm_tela: 'Usuários x Perfis',
            ds_rota: '/oizap/gestao-usuarios-perfis',
            ds_descricao: 'Associar usuários aos perfis de acesso',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        }
    ];

    res.json({
        statuscode: 200,
        message: 'Associações listadas com sucesso (dados mock)',
        data: dadosMock
    });
});

// Rota original comentada para debug posterior
// router.get('/perfis-telas', PerfilTelaController.listarAssociacoes);
router.get('/perfis-telas/:nr_perfil_tela', authApi, PerfilTelaController.buscarAssociacaoPorId);

// Rota para associar múltiplas telas a um perfil
router.post('/perfis-telas/multiplas', authApi, PerfilTelaController.associarMultiplasTelas);

export { router as PerfisTelaRoutes };
