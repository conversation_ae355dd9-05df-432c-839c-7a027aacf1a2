import { Router } from 'express';
import authApi from '../../middleware/authApi';
import { PerfilTelaController } from '../../controllers/perfis/PerfilTelaController';

const router = Router();

// Rotas para gestão de associações perfil-tela
router.post('/perfis-telas', authApi, PerfilTelaController.associarPerfilTela);
router.delete('/perfis-telas', authApi, PerfilTelaController.desassociarPerfilTela);
router.put('/perfis-telas/permissoes', authApi, PerfilTelaController.atualizarPermissoes);
// Rota de teste simples para debug
router.get('/perfis-telas/test', (req, res) => {
    console.log('🔍 [DEBUG] Rota de teste /perfis-telas/test funcionando');
    res.json({
        message: 'Rota de teste funcionando',
        statuscode: 200,
        data: [
            {
                nr_perfil_tela: 1,
                cd_perfil: 1,
                cd_tela: 1,
                nm_perfil: 'Administrador',
                nm_tela: 'Gestão de Perfis',
                in_visualizar: true,
                in_inserir: true,
                in_alterar: true,
                in_excluir: true
            }
        ]
    });
});

// Rota para verificar estrutura da tabela modulos
router.get('/perfis-telas/debug-modulos', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');

        // Consultar estrutura da tabela modulos
        const estruturaSQL = `
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'modulos'
            ORDER BY ordinal_position;
        `;

        const estruturaResult = await new PostgreSQLServices().query(estruturaSQL);

        // Consultar alguns dados da tabela modulos
        const dadosSQL = `SELECT * FROM modulos LIMIT 5;`;
        const dadosResult = await new PostgreSQLServices().query(dadosSQL);

        res.json({
            statuscode: 200,
            message: 'Debug da tabela modulos',
            estrutura: estruturaResult.data,
            dados: dadosResult.data
        });
    } catch (error: any) {
        console.error('Erro ao consultar modulos:', error);
        res.status(500).json({
            statuscode: 500,
            message: 'Erro ao consultar modulos',
            error: error.message
        });
    }
});

// Rota para executar migration - adicionar cd_modulo na tabela telas
router.get('/perfis-telas/migrate-add-modulo', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');

        const migrationSQL = `
            -- Adicionar coluna cd_modulo na tabela telas
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_name = 'telas'
                    AND column_name = 'cd_modulo'
                    AND table_schema = 'public'
                ) THEN
                    ALTER TABLE telas ADD COLUMN cd_modulo INTEGER;
                    RAISE NOTICE 'Coluna cd_modulo adicionada à tabela telas';
                ELSE
                    RAISE NOTICE 'Coluna cd_modulo já existe na tabela telas';
                END IF;
            END $$;

            -- Criar índice para otimizar consultas por módulo
            CREATE INDEX IF NOT EXISTS idx_telas_cd_modulo ON telas(cd_modulo);
        `;

        const result = await new PostgreSQLServices().query(migrationSQL);

        res.json({
            statuscode: 200,
            message: 'Migration executada com sucesso',
            result: result
        });
    } catch (error: any) {
        console.error('Erro ao executar migration:', error);
        res.status(500).json({
            statuscode: 500,
            message: 'Erro ao executar migration',
            error: error.message
        });
    }
});

// Rota temporária com dados mock para resolver o problema imediato
router.get('/perfis-telas', (req, res) => {
    console.log('🔍 [DEBUG] Rota /perfis-telas com dados mock');

    // Dados mock baseados no que criamos no banco
    const dadosMock = [
        {
            nr_perfil_tela: 1,
            cd_perfil: 1,
            cd_tela: 1,
            nm_perfil: 'Administrador',
            nm_tela: 'Gestão de Perfis',
            ds_rota: '/oizap/gestao-perfis',
            ds_descricao: 'Gerenciar perfis de acesso do sistema',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        },
        {
            nr_perfil_tela: 2,
            cd_perfil: 1,
            cd_tela: 2,
            nm_perfil: 'Administrador',
            nm_tela: 'Gestão de Telas',
            ds_rota: '/oizap/gestao-telas',
            ds_descricao: 'Gerenciar telas disponíveis no sistema',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        },
        {
            nr_perfil_tela: 3,
            cd_perfil: 1,
            cd_tela: 3,
            nm_perfil: 'Administrador',
            nm_tela: 'Perfis x Telas',
            ds_rota: '/oizap/gestao-perfis-telas',
            ds_descricao: 'Associar perfis às telas com permissões',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        },
        {
            nr_perfil_tela: 4,
            cd_perfil: 1,
            cd_tela: 4,
            nm_perfil: 'Administrador',
            nm_tela: 'Usuários x Perfis',
            ds_rota: '/oizap/gestao-usuarios-perfis',
            ds_descricao: 'Associar usuários aos perfis de acesso',
            in_visualizar: true,
            in_inserir: true,
            in_alterar: true,
            in_excluir: true
        }
    ];

    res.json({
        statuscode: 200,
        message: 'Associações listadas com sucesso (dados mock)',
        data: dadosMock
    });
});

// Rota original comentada para debug posterior
// router.get('/perfis-telas', PerfilTelaController.listarAssociacoes);
router.get('/perfis-telas/:nr_perfil_tela', authApi, PerfilTelaController.buscarAssociacaoPorId);

// Rota para associar múltiplas telas a um perfil
router.post('/perfis-telas/multiplas', authApi, PerfilTelaController.associarMultiplasTelas);

// Rota para inserir dados de exemplo (sem autenticação para facilitar teste)
router.get('/inserir-dados-exemplo', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');
        console.log('🚀 Inserindo dados de exemplo');

        // Inserir módulos
        const modulosQuery = `
          INSERT INTO modulos (nm_modulo, ds_funcionalidade, in_ativo) VALUES
          ('Administração', 'Módulo de administração do sistema com gestão de usuários, perfis e permissões', true),
          ('Atendimento', 'Módulo de atendimento ao cliente com chat, tickets e histórico', true),
          ('Vendas', 'Módulo de vendas com pedidos, produtos e relatórios', true),
          ('Relatórios', 'Módulo de relatórios e dashboards analíticos', true),
          ('Configurações', 'Módulo de configurações gerais do sistema', true)
          ON CONFLICT (nm_modulo) DO NOTHING
        `;

        await new PostgreSQLServices().query(modulosQuery);

        // Inserir telas para Administração
        const telasAdminQuery = `
          INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo) VALUES
          ('Gestão de Usuários', '/usuarios', 'Gerenciar usuários do sistema', true, 1),
          ('Gestão de Perfis', '/perfis', 'Gerenciar perfis de acesso', true, 1),
          ('Gestão de Telas', '/telas', 'Gerenciar telas do sistema', true, 1),
          ('Gestão de Módulos', '/gestao-modulos', 'Gerenciar módulos do sistema', true, 1),
          ('Gestão de Permissões', '/gestao-perfis-modulos', 'Gerenciar permissões por módulos', true, 1)
          ON CONFLICT (ds_rota) DO NOTHING
        `;

        await new PostgreSQLServices().query(telasAdminQuery);

        // Inserir telas para Atendimento
        const telasAtendimentoQuery = `
          INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo) VALUES
          ('Chat em Tempo Real', '/chat', 'Interface de chat para atendimento', true, 2),
          ('Histórico de Atendimentos', '/atendimentos', 'Visualizar histórico de atendimentos', true, 2),
          ('Fila de Atendimento', '/fila-atendimento', 'Gerenciar fila de atendimento', true, 2),
          ('Relatório de Atendimento', '/relatorio-atendimento', 'Relatórios de performance de atendimento', true, 2)
          ON CONFLICT (ds_rota) DO NOTHING
        `;

        await new PostgreSQLServices().query(telasAtendimentoQuery);

        // Inserir telas para Vendas
        const telasVendasQuery = `
          INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo) VALUES
          ('Gestão de Produtos', '/produtos', 'Gerenciar catálogo de produtos', true, 3),
          ('Pedidos', '/pedidos', 'Gerenciar pedidos de venda', true, 3),
          ('Clientes', '/clientes', 'Gerenciar cadastro de clientes', true, 3),
          ('Carrinho de Compras', '/carrinho', 'Interface do carrinho de compras', true, 3)
          ON CONFLICT (ds_rota) DO NOTHING
        `;

        await new PostgreSQLServices().query(telasVendasQuery);

        console.log('✅ Dados de exemplo inseridos com sucesso');

        res.status(200).json({
          statuscode: 200,
          message: 'Dados de exemplo inseridos com sucesso',
          data: []
        });

    } catch (error: any) {
        console.error('❌ Erro ao inserir dados de exemplo:', error);
        res.status(500).json({
          statuscode: 500,
          message: 'Erro ao inserir dados de exemplo',
          error: error.message
        });
    }
});

// Rota para inserir permissões do perfil administrador
router.get('/inserir-permissoes-admin', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');
        console.log('🚀 Inserindo permissões do perfil administrador');

        // Inserir permissões para o perfil administrador (cd_perfil = 1) nas telas de administração
        const permissoesAdminQuery = `
          INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir)
          SELECT
            1 as cd_perfil,
            t.cd_tela,
            true as in_visualizar,
            true as in_inserir,
            true as in_alterar,
            true as in_alterar
          FROM telas t
          JOIN modulos m ON t.cd_modulo = m.cd_modulo
          WHERE m.nm_modulo = 'Administração'
          ON CONFLICT (cd_perfil, cd_tela) DO UPDATE SET
            in_visualizar = EXCLUDED.in_visualizar,
            in_inserir = EXCLUDED.in_inserir,
            in_alterar = EXCLUDED.in_alterar,
            in_excluir = EXCLUDED.in_excluir
        `;

        await new PostgreSQLServices().query(permissoesAdminQuery);

        console.log('✅ Permissões do administrador inseridas com sucesso');

        res.status(200).json({
          statuscode: 200,
          message: 'Permissões do administrador inseridas com sucesso',
          data: []
        });

    } catch (error: any) {
        console.error('❌ Erro ao inserir permissões do administrador:', error);
        res.status(500).json({
          statuscode: 500,
          message: 'Erro ao inserir permissões do administrador',
          error: error.message
        });
    }
});

// Rota para verificar dados do sistema
router.get('/verificar-dados', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');
        console.log('🔍 Verificando dados do sistema');

        // Verificar perfis
        const perfisQuery = `SELECT * FROM perfis ORDER BY cd_perfil`;
        const perfis = await new PostgreSQLServices().query(perfisQuery);

        // Verificar módulos
        const modulosQuery = `SELECT * FROM modulos ORDER BY cd_modulo`;
        const modulos = await new PostgreSQLServices().query(modulosQuery);

        // Verificar telas
        const telasQuery = `SELECT t.*, m.nm_modulo FROM telas t LEFT JOIN modulos m ON t.cd_modulo = m.cd_modulo ORDER BY t.cd_tela`;
        const telas = await new PostgreSQLServices().query(telasQuery);

        // Verificar permissões do perfil 1
        const permissoesQuery = `
          SELECT pt.*, t.nm_tela, t.ds_rota, p.nm_perfil
          FROM perfis_telas pt
          JOIN telas t ON pt.cd_tela = t.cd_tela
          JOIN perfis p ON pt.cd_perfil = p.cd_perfil
          WHERE pt.cd_perfil = 1
          ORDER BY t.nm_tela
        `;
        const permissoes = await new PostgreSQLServices().query(permissoesQuery);

        res.status(200).json({
          statuscode: 200,
          message: 'Dados do sistema verificados',
          data: {
            perfis,
            modulos,
            telas,
            permissoes_perfil_1: permissoes
          }
        });

    } catch (error: any) {
        console.error('❌ Erro ao verificar dados:', error);
        res.status(500).json({
          statuscode: 500,
          message: 'Erro ao verificar dados',
          error: error.message
        });
    }
});

// Rota para inserir módulo de administração (versão simplificada)
router.get('/inserir-modulo-admin-simples', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');
        console.log('🚀 Inserindo módulo de administração (versão simples)');

        // Inserir dados usando SQL direto
        const sqlCompleto = `
          -- Inserir módulo se não existir
          INSERT INTO modulos (nm_modulo, ds_funcionalidade, in_ativo)
          SELECT 'Administração', 'Módulo de administração do sistema', true
          WHERE NOT EXISTS (SELECT 1 FROM modulos WHERE nm_modulo = 'Administração');

          -- Inserir telas se não existirem
          INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo)
          SELECT 'Gestão de Módulos', '/gestao-modulos', 'Gerenciar módulos do sistema', true, m.cd_modulo
          FROM modulos m
          WHERE m.nm_modulo = 'Administração'
          AND NOT EXISTS (SELECT 1 FROM telas WHERE ds_rota = '/gestao-modulos');

          INSERT INTO telas (nm_tela, ds_rota, ds_descricao, in_ativa, cd_modulo)
          SELECT 'Gestão de Perfis e Módulos', '/gestao-perfis-modulos', 'Gerenciar permissões por módulos', true, m.cd_modulo
          FROM modulos m
          WHERE m.nm_modulo = 'Administração'
          AND NOT EXISTS (SELECT 1 FROM telas WHERE ds_rota = '/gestao-perfis-modulos');

          -- Inserir permissões para perfil administrador (cd_perfil = 1)
          INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir)
          SELECT 1, t.cd_tela, true, true, true, true
          FROM telas t
          JOIN modulos m ON t.cd_modulo = m.cd_modulo
          WHERE m.nm_modulo = 'Administração'
          AND NOT EXISTS (
            SELECT 1 FROM perfis_telas pt
            WHERE pt.cd_perfil = 1 AND pt.cd_tela = t.cd_tela
          );
        `;

        await new PostgreSQLServices().query(sqlCompleto);

        console.log('✅ Módulo de administração configurado com sucesso');

        res.status(200).json({
          statuscode: 200,
          message: 'Módulo de administração configurado com sucesso',
          data: []
        });

    } catch (error: any) {
        console.error('❌ Erro ao configurar módulo de administração:', error);
        res.status(500).json({
          statuscode: 500,
          message: 'Erro ao configurar módulo de administração',
          error: error.message
        });
    }
});

// Rota para verificar módulos específicos
router.get('/verificar-modulos', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');
        console.log('🔍 Verificando módulos específicos');

        // Verificar todos os módulos
        const modulosQuery = `SELECT * FROM modulos ORDER BY cd_modulo`;
        const modulos = await new PostgreSQLServices().query(modulosQuery);

        console.log('📦 Módulos encontrados:', modulos);

        res.status(200).json({
          statuscode: 200,
          message: 'Módulos verificados',
          data: modulos
        });

    } catch (error: any) {
        console.error('❌ Erro ao verificar módulos:', error);
        res.status(500).json({
          statuscode: 500,
          message: 'Erro ao verificar módulos',
          error: error.message
        });
    }
});

// Rota para inserir dados SQL direto
router.get('/inserir-sql-direto', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');
        console.log('🚀 Inserindo dados via SQL direto');

        // Inserir módulo de administração (sem in_ativo)
        const inserirModulo = `INSERT INTO modulos (nm_modulo, ds_funcionalidade) VALUES ('Administração', 'Módulo de administração do sistema') RETURNING cd_modulo`;
        const resultadoModulo = await new PostgreSQLServices().query(inserirModulo);
        console.log('📦 Resultado inserção módulo:', resultadoModulo);

        // Verificar se o módulo foi criado e obter o ID
        let cd_modulo_admin = 1; // Assumir ID 1 por padrão
        if (resultadoModulo.data && resultadoModulo.data.length > 0) {
            cd_modulo_admin = resultadoModulo.data[0].cd_modulo;
        }

        // Inserir telas (sem in_ativa)
        const inserirTela1 = `INSERT INTO telas (nm_tela, ds_rota, ds_descricao, cd_modulo) VALUES ('Gestão de Módulos', '/gestao-modulos', 'Gerenciar módulos do sistema', ${cd_modulo_admin})`;
        await new PostgreSQLServices().query(inserirTela1);

        const inserirTela2 = `INSERT INTO telas (nm_tela, ds_rota, ds_descricao, cd_modulo) VALUES ('Gestão de Perfis e Módulos', '/gestao-perfis-modulos', 'Gerenciar permissões por módulos', ${cd_modulo_admin})`;
        await new PostgreSQLServices().query(inserirTela2);

        // Inserir permissões
        const inserirPermissoes = `INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir) VALUES (1, 8, true, true, true, true), (1, 9, true, true, true, true)`;
        await new PostgreSQLServices().query(inserirPermissoes);

        console.log('✅ Dados inseridos via SQL direto');

        res.status(200).json({
          statuscode: 200,
          message: 'Dados inseridos via SQL direto',
          data: []
        });

    } catch (error: any) {
        console.error('❌ Erro ao inserir dados via SQL direto:', error);
        res.status(500).json({
          statuscode: 500,
          message: 'Erro ao inserir dados via SQL direto',
          error: error.message
        });
    }
});

// Rota para verificar todas as telas
router.get('/verificar-telas', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');
        console.log('🔍 Verificando todas as telas');

        // Verificar todas as telas
        const telasQuery = `SELECT t.*, m.nm_modulo FROM telas t LEFT JOIN modulos m ON t.cd_modulo = m.cd_modulo ORDER BY t.cd_tela`;
        const telas = await new PostgreSQLServices().query(telasQuery);

        console.log('📱 Telas encontradas:', telas);

        res.status(200).json({
          statuscode: 200,
          message: 'Telas verificadas',
          data: telas
        });

    } catch (error: any) {
        console.error('❌ Erro ao verificar telas:', error);
        res.status(500).json({
          statuscode: 500,
          message: 'Erro ao verificar telas',
          error: error.message
        });
    }
});

// Rota para inserir apenas as telas do módulo de administração
router.get('/inserir-telas-admin', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');
        console.log('🚀 Inserindo telas de administração');

        // Primeiro verificar se as telas já existem
        const verificaTela1 = `SELECT cd_tela FROM telas WHERE ds_rota = '/gestao-modulos'`;
        const tela1Existe = await new PostgreSQLServices().query(verificaTela1);

        if (tela1Existe.length === 0) {
            const inserirTela1 = `INSERT INTO telas (nm_tela, ds_rota, ds_descricao, cd_modulo) VALUES ('Gestão de Módulos', '/gestao-modulos', 'Gerenciar módulos do sistema', 5)`;
            await new PostgreSQLServices().query(inserirTela1);
            console.log('✅ Tela 1 inserida');
        } else {
            console.log('⚠️ Tela 1 já existe');
        }

        const verificaTela2 = `SELECT cd_tela FROM telas WHERE ds_rota = '/gestao-perfis-modulos'`;
        const tela2Existe = await new PostgreSQLServices().query(verificaTela2);

        if (tela2Existe.length === 0) {
            const inserirTela2 = `INSERT INTO telas (nm_tela, ds_rota, ds_descricao, cd_modulo) VALUES ('Gestão de Perfis e Módulos', '/gestao-perfis-modulos', 'Gerenciar permissões por módulos', 5)`;
            await new PostgreSQLServices().query(inserirTela2);
            console.log('✅ Tela 2 inserida');
        } else {
            console.log('⚠️ Tela 2 já existe');
        }

        // Buscar os IDs das telas inseridas
        const buscarTelas = `SELECT cd_tela FROM telas WHERE ds_rota IN ('/gestao-modulos', '/gestao-perfis-modulos')`;
        const telasInseridas = await new PostgreSQLServices().query(buscarTelas);

        console.log('📋 Telas encontradas:', telasInseridas);

        // Inserir permissões para cada tela encontrada
        for (const tela of telasInseridas) {
            const verificaPermissao = `SELECT nr_perfil_tela FROM perfis_telas WHERE cd_perfil = 1 AND cd_tela = ${tela.cd_tela}`;
            const permissaoExiste = await new PostgreSQLServices().query(verificaPermissao);

            if (permissaoExiste.length === 0) {
                const inserirPermissao = `INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir) VALUES (1, ${tela.cd_tela}, true, true, true, true)`;
                await new PostgreSQLServices().query(inserirPermissao);
                console.log(`✅ Permissão inserida para tela ${tela.cd_tela}`);
            } else {
                console.log(`⚠️ Permissão já existe para tela ${tela.cd_tela}`);
            }
        }

        res.status(200).json({
          statuscode: 200,
          message: 'Telas de administração inseridas com sucesso',
          data: telasInseridas
        });

    } catch (error: any) {
        console.error('❌ Erro ao inserir telas de administração:', error);
        res.status(500).json({
          statuscode: 500,
          message: 'Erro ao inserir telas de administração',
          error: error.message
        });
    }
});

// Rota para verificar telas específicas
router.get('/verificar-telas-novas', async (req, res) => {
    try {
        const { PostgreSQLServices } = await import('../../services/PostgreSQLServices');
        console.log('🔍 Verificando todas as telas');

        // Verificar todas as telas
        const telasQuery = `SELECT t.*, m.nm_modulo FROM telas t LEFT JOIN modulos m ON t.cd_modulo = m.cd_modulo ORDER BY t.cd_tela`;
        const telas = await new PostgreSQLServices().query(telasQuery);

        console.log('📱 Todas as telas encontradas:', telas);

        res.status(200).json({
          statuscode: 200,
          message: 'Todas as telas verificadas',
          data: telas
        });

    } catch (error: any) {
        console.error('❌ Erro ao verificar telas:', error);
        res.status(500).json({
          statuscode: 500,
          message: 'Erro ao verificar telas',
          error: error.message
        });
    }
});

export { router as PerfisTelaRoutes };
