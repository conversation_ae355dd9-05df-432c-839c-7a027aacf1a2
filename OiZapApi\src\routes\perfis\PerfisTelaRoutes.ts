import { Router } from 'express';
import authApi from '../../middleware/authApi';
import { PerfilTelaController } from '../../controllers/perfis/PerfilTelaController';

const router = Router();

// Rotas para gestão de associações perfil-tela
router.post('/perfis-telas', authApi, PerfilTelaController.associarPerfilTela);
router.delete('/perfis-telas', authApi, PerfilTelaController.desassociarPerfilTela);
router.put('/perfis-telas/permissoes', authApi, PerfilTelaController.atualizarPermissoes);

// Rotas principais da API
router.get('/perfis-telas', authApi, PerfilTelaController.listarAssociacoes);
router.get('/perfis-telas/:nr_perfil_tela', authApi, PerfilTelaController.buscarAssociacaoPorId);

// Rota para associar múltiplas telas a um perfil
router.post('/perfis-telas/multiplas', authApi, PerfilTelaController.associarMultiplasTelas);

export { router as PerfisTelaRoutes };
