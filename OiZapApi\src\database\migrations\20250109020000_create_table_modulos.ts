import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  try {
    console.log('Verificando se tabela modulos existe...');
    
    const hasTable = await knex.schema.hasTable('modulos');
    
    if (!hasTable) {
      console.log('Criando tabela modulos...');
      await knex.schema.createTable('modulos', (table) => {
        table.increments('cd_modulo').primary();
        table.string('nm_modulo', 100).notNullable().unique();
        table.text('ds_funcionalidade').nullable();
        table.decimal('vl_modulo', 10, 2).defaultTo(0.00);
        table.boolean('in_ativo').defaultTo(true);
        table.timestamps(true, true);
      });
      
      // Criar índices
      await knex.schema.alterTable('modulos', (table) => {
        table.index('nm_modulo', 'idx_modulos_nm_modulo');
        table.index('in_ativo', 'idx_modulos_in_ativo');
      });
      
      console.log('Tabela modulos criada com sucesso!');
    } else {
      console.log('Tabela modulos já existe.');
    }
  } catch (error) {
    console.error('Erro na migration create_table_modulos:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  try {
    await knex.schema.dropTableIfExists('modulos');
    console.log('Tabela modulos removida.');
  } catch (error) {
    console.error('Erro ao remover tabela modulos:', error);
    throw error;
  }
}
