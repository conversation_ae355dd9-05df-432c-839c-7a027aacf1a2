import { Router } from 'express';
import authApi from '../../middleware/authApi';
import { TelaController } from '../../controllers/telas/TelaController';

const router = Router();

// Rotas para gestão de telas
router.post('/telas', authApi, TelaController.criarTela);
router.get('/telas', authApi, TelaController.listarTelas);
router.get('/telas/:cd_tela', authApi, TelaController.buscarTelaPorId);
router.put('/telas/:cd_tela', authApi, TelaController.atualizarTela);
router.delete('/telas/:cd_tela', authApi, TelaController.deletarTela);

// Rotas para listar relacionamentos da tela
router.get('/telas/:cd_tela/perfis', authApi, TelaController.listarPerfisDaTela);

// Rota para verificar acesso de usuário a uma tela
router.get('/telas/acesso/:cd_usuario/:ds_rota', authApi, TelaController.verificarAcesso);

// Rota para ativar/desativar tela
router.patch('/telas/:cd_tela/status', authApi, TelaController.ativarDesativarTela);

export { router as TelasRoutes };
