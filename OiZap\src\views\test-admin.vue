<template>
    <div class="p-8">
        <h1 class="text-2xl font-bold mb-4">Teste do Sistema Administrativo</h1>
        
        <div class="space-y-4">
            <div>
                <h2 class="text-lg font-semibold">1. Teste de Conexão com API</h2>
                <button @click="testarConexao" class="btn btn-primary mr-2">Testar Conexão</button>
                <span v-if="statusConexao" :class="statusConexao.sucesso ? 'text-green-600' : 'text-red-600'">
                    {{ statusConexao.mensagem }}
                </span>
            </div>

            <div>
                <h2 class="text-lg font-semibold">2. Teste de Perfis</h2>
                <button @click="testarPerfis" class="btn btn-primary mr-2">Listar Perfis</button>
                <span v-if="statusPerfis" :class="statusPerfis.sucesso ? 'text-green-600' : 'text-red-600'">
                    {{ statusPerfis.mensagem }}
                </span>
                <div v-if="perfis.length > 0" class="mt-2">
                    <p>Perfis encontrados: {{ perfis.length }}</p>
                    <ul class="list-disc list-inside">
                        <li v-for="perfil in perfis" :key="perfil.cd_perfil">
                            {{ perfil.nm_perfil }} (ID: {{ perfil.cd_perfil }})
                        </li>
                    </ul>
                </div>
            </div>

            <div>
                <h2 class="text-lg font-semibold">3. Teste de Menu para Usuário</h2>
                <div class="flex gap-2 mb-2">
                    <input 
                        v-model="usuarioTeste" 
                        type="number" 
                        placeholder="ID do usuário (ex: 87)" 
                        class="form-control w-48"
                    />
                    <button @click="testarMenu" class="btn btn-primary">Testar Menu</button>
                </div>
                <span v-if="statusMenu" :class="statusMenu.sucesso ? 'text-green-600' : 'text-red-600'">
                    {{ statusMenu.mensagem }}
                </span>
                <div v-if="menuUsuario" class="mt-2">
                    <p>Menu gerado:</p>
                    <pre class="bg-gray-100 p-2 rounded text-sm">{{ JSON.stringify(menuUsuario, null, 2) }}</pre>
                </div>
            </div>

            <div>
                <h2 class="text-lg font-semibold">4. Verificar Tabelas do Banco</h2>
                <div class="flex gap-2 mb-2">
                    <button @click="verificarTabelas" class="btn btn-primary">Verificar Tabelas</button>
                    <button @click="criarTabelas" class="btn btn-warning">Criar Tabelas</button>
                    <button @click="inserirDadosIniciais" class="btn btn-success">Inserir Dados Iniciais</button>
                </div>
                <span v-if="statusTabelas" :class="statusTabelas.sucesso ? 'text-green-600' : 'text-red-600'">
                    {{ statusTabelas.mensagem }}
                </span>
                <div v-if="infoTabelas" class="mt-2">
                    <details>
                        <summary class="cursor-pointer text-blue-600">Ver informações das tabelas</summary>
                        <pre class="bg-gray-100 p-2 rounded text-sm mt-2">{{ JSON.stringify(infoTabelas, null, 2) }}</pre>
                    </details>
                </div>
            </div>

            <div>
                <h2 class="text-lg font-semibold">5. Informações do LocalStorage</h2>
                <button @click="verificarLocalStorage" class="btn btn-primary mr-2">Verificar LocalStorage</button>
                <div v-if="infoLocalStorage" class="mt-2">
                    <p><strong>Usuário:</strong> {{ infoLocalStorage.usuario }}</p>
                    <p><strong>Código Usuário:</strong> {{ infoLocalStorage.cd_usuario }}</p>
                    <p><strong>Telas Permitidas:</strong> {{ infoLocalStorage.telasPermitidas?.length || 0 }}</p>
                    <div v-if="infoLocalStorage.telasPermitidas?.length > 0" class="mt-2">
                        <details>
                            <summary class="cursor-pointer text-blue-600">Ver telas permitidas</summary>
                            <pre class="bg-gray-100 p-2 rounded text-sm mt-2">{{ JSON.stringify(infoLocalStorage.telasPermitidas, null, 2) }}</pre>
                        </details>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import AccessControlService from '@/services/administracao/AccessControlService';

// Estados
const statusConexao = ref(null);
const statusPerfis = ref(null);
const statusMenu = ref(null);
const statusTabelas = ref(null);
const perfis = ref([]);
const usuarioTeste = ref(87);
const menuUsuario = ref(null);
const infoLocalStorage = ref(null);
const infoTabelas = ref(null);

// Métodos
async function testarConexao() {
    try {
        statusConexao.value = { sucesso: false, mensagem: 'Testando...' };
        
        // Teste simples de conexão
        const response = await fetch('http://localhost:3100/oizap');
        const text = await response.text();
        
        if (response.ok) {
            statusConexao.value = { sucesso: true, mensagem: 'Conexão OK! ' + text };
        } else {
            statusConexao.value = { sucesso: false, mensagem: 'Erro na conexão: ' + response.status };
        }
    } catch (error) {
        statusConexao.value = { sucesso: false, mensagem: 'Erro: ' + error.message };
    }
}

async function testarPerfis() {
    try {
        statusPerfis.value = { sucesso: false, mensagem: 'Carregando...' };
        
        const response = await AccessControlService.listarPerfis();
        
        if (response.statuscode === 200) {
            perfis.value = response.data;
            statusPerfis.value = { sucesso: true, mensagem: `Sucesso! ${response.data.length} perfis encontrados` };
        } else {
            statusPerfis.value = { sucesso: false, mensagem: 'Erro: ' + response.message };
        }
    } catch (error) {
        statusPerfis.value = { sucesso: false, mensagem: 'Erro: ' + error.message };
        console.error('Erro ao testar perfis:', error);
    }
}

async function testarMenu() {
    try {
        statusMenu.value = { sucesso: false, mensagem: 'Carregando...' };
        
        const response = await AccessControlService.obterMenuPorUsuario(usuarioTeste.value);
        
        if (response.menu) {
            menuUsuario.value = response;
            statusMenu.value = { sucesso: true, mensagem: `Menu gerado com ${response.menu.length} itens` };
        } else {
            statusMenu.value = { sucesso: false, mensagem: 'Nenhum menu retornado' };
        }
    } catch (error) {
        statusMenu.value = { sucesso: false, mensagem: 'Erro: ' + error.message };
        console.error('Erro ao testar menu:', error);
    }
}

async function verificarTabelas() {
    try {
        statusTabelas.value = { sucesso: false, mensagem: 'Verificando...' };

        const response = await fetch('http://localhost:3100/oizap/debug/verificar-tabelas');
        const data = await response.json();

        if (response.ok) {
            infoTabelas.value = data.data;
            statusTabelas.value = { sucesso: true, mensagem: 'Tabelas verificadas com sucesso!' };
        } else {
            statusTabelas.value = { sucesso: false, mensagem: 'Erro: ' + data.message };
        }
    } catch (error) {
        statusTabelas.value = { sucesso: false, mensagem: 'Erro: ' + error.message };
    }
}

async function criarTabelas() {
    try {
        statusTabelas.value = { sucesso: false, mensagem: 'Criando tabelas...' };

        const response = await fetch('http://localhost:3100/oizap/debug/criar-tabelas', {
            method: 'POST'
        });
        const data = await response.json();

        if (response.ok) {
            statusTabelas.value = { sucesso: true, mensagem: 'Tabelas criadas com sucesso!' };
            // Verificar novamente
            await verificarTabelas();
        } else {
            statusTabelas.value = { sucesso: false, mensagem: 'Erro: ' + data.message };
        }
    } catch (error) {
        statusTabelas.value = { sucesso: false, mensagem: 'Erro: ' + error.message };
    }
}

async function inserirDadosIniciais() {
    try {
        statusTabelas.value = { sucesso: false, mensagem: 'Inserindo dados iniciais...' };

        const response = await fetch('http://localhost:3100/oizap/debug/inserir-dados-iniciais', {
            method: 'POST'
        });
        const data = await response.json();

        if (response.ok) {
            statusTabelas.value = { sucesso: true, mensagem: 'Dados iniciais inseridos com sucesso!' };
            // Verificar novamente
            await verificarTabelas();
        } else {
            statusTabelas.value = { sucesso: false, mensagem: 'Erro: ' + data.message };
        }
    } catch (error) {
        statusTabelas.value = { sucesso: false, mensagem: 'Erro: ' + error.message };
    }
}

function verificarLocalStorage() {
    infoLocalStorage.value = {
        usuario: localStorage.getItem('usuario'),
        cd_usuario: localStorage.getItem('codusuario'),
        telasPermitidas: JSON.parse(localStorage.getItem('telasPermitidas') || '[]')
    };
}
</script>
