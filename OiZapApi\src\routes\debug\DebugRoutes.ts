import { Router, Request, Response } from 'express';
import { PostgreSQLServices } from '../../services/PostgreSQLServices';
import { sucesso, erroInterno } from '../../interfaces/IRetorno';

const router = Router();

// Endpoint para verificar se as tabelas do controle de acesso existem
router.get('/oizap/debug/verificar-tabelas', async (req: Request, res: Response) => {
    try {
        const db = new PostgreSQLServices();
        
        // Verificar se as tabelas existem
        const tabelasQuery = `
            SELECT 
                table_name,
                table_type
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('perfis', 'telas', 'perfis_telas', 'usuarios_perfis')
            ORDER BY table_name
        `;
        
        const tabelas = await db.query(tabelasQuery);
        
        // Verificar dados existentes (se as tabelas existirem)
        let dados: any = {};

        if (tabelas.data && tabelas.data.length > 0) {
            for (const tabela of tabelas.data) {
                try {
                    const countQuery = `SELECT count(*) as total FROM ${tabela.table_name}`;
                    const count = await db.query(countQuery);
                    dados[tabela.table_name] = count.data[0]?.total || 0;
                } catch (error) {
                    dados[tabela.table_name] = 'Erro ao contar';
                }
            }

            // Verificar dados específicos do usuário 87
            try {
                const usuario87Query = `
                    SELECT
                        up.cd_usuario_perfil,
                        up.cd_usuario,
                        up.cd_perfil,
                        p.nm_perfil,
                        up.created_at
                    FROM usuarios_perfis up
                    JOIN perfis p ON p.cd_perfil = up.cd_perfil
                    WHERE up.cd_usuario = 87
                `;
                const usuario87 = await db.query(usuario87Query);
                dados['usuario_87_perfis'] = usuario87.data || [];

                // Verificar telas do usuário 87
                const telasUsuario87Query = `
                    SELECT DISTINCT
                        t.cd_tela,
                        t.nm_tela,
                        t.ds_rota,
                        pt.in_visualizar,
                        pt.in_inserir,
                        pt.in_alterar,
                        pt.in_excluir
                    FROM usuarios_perfis up
                    JOIN perfis_telas pt ON pt.cd_perfil = up.cd_perfil
                    JOIN telas t ON t.cd_tela = pt.cd_tela
                    WHERE up.cd_usuario = 87
                    ORDER BY t.nm_tela
                `;
                const telasUsuario87 = await db.query(telasUsuario87Query);
                dados['usuario_87_telas'] = telasUsuario87.data || [];

            } catch (error: any) {
                dados['usuario_87_erro'] = error.message;
            }
        }
        
        const resultado = {
            tabelas_existentes: tabelas.data || [],
            dados_tabelas: dados,
            timestamp: new Date().toISOString()
        };
        
        return res.status(200).send(sucesso(resultado, 'Verificação de tabelas concluída'));
        
    } catch (error: any) {
        console.error('Erro ao verificar tabelas:', error);
        return res.status(500).send(erroInterno(error));
    }
});

// Endpoint para criar as tabelas se não existirem
router.post('/oizap/debug/criar-tabelas', async (req: Request, res: Response) => {
    try {
        const db = new PostgreSQLServices();
        
        const queries = [
            // Criar tabela perfis
            `CREATE TABLE IF NOT EXISTS perfis (
                cd_perfil SERIAL PRIMARY KEY,
                nm_perfil VARCHAR(100) NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Criar tabela telas
            `CREATE TABLE IF NOT EXISTS telas (
                cd_tela SERIAL PRIMARY KEY,
                nm_tela VARCHAR(100) NOT NULL,
                ds_rota VARCHAR(255) NOT NULL UNIQUE,
                ds_descricao TEXT,
                in_ativo BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Criar tabela perfis_telas
            `CREATE TABLE IF NOT EXISTS perfis_telas (
                cd_perfil_tela SERIAL PRIMARY KEY,
                cd_perfil INTEGER NOT NULL REFERENCES perfis(cd_perfil) ON DELETE CASCADE,
                cd_tela INTEGER NOT NULL REFERENCES telas(cd_tela) ON DELETE CASCADE,
                in_visualizar BOOLEAN DEFAULT false,
                in_inserir BOOLEAN DEFAULT false,
                in_alterar BOOLEAN DEFAULT false,
                in_excluir BOOLEAN DEFAULT false,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(cd_perfil, cd_tela)
            )`,
            
            // Criar tabela usuarios_perfis
            `CREATE TABLE IF NOT EXISTS usuarios_perfis (
                cd_usuario_perfil SERIAL PRIMARY KEY,
                cd_usuario INTEGER NOT NULL,
                cd_perfil INTEGER NOT NULL REFERENCES perfis(cd_perfil) ON DELETE CASCADE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(cd_usuario, cd_perfil)
            )`
        ];
        
        const resultados = [];
        
        for (const query of queries) {
            try {
                const resultado = await db.query(query);
                resultados.push({ query: query.substring(0, 50) + '...', sucesso: true });
            } catch (error: any) {
                resultados.push({ query: query.substring(0, 50) + '...', sucesso: false, erro: error.message });
            }
        }
        
        return res.status(200).send(sucesso(resultados, 'Criação de tabelas concluída'));
        
    } catch (error: any) {
        console.error('Erro ao criar tabelas:', error);
        return res.status(500).send(erroInterno(error));
    }
});

// Endpoint para inserir dados iniciais
router.post('/oizap/debug/inserir-dados-iniciais', async (req: Request, res: Response) => {
    try {
        const db = new PostgreSQLServices();
        
        const queries = [
            // Inserir perfil administrativo
            `INSERT INTO perfis (nm_perfil) VALUES ('Administrador') ON CONFLICT (nm_perfil) DO NOTHING`,
            
            // Inserir telas administrativas
            `INSERT INTO telas (nm_tela, ds_rota, ds_descricao) VALUES 
                ('Gestão de Perfis', '/oizap/gestao-perfis', 'Gerenciar perfis de acesso do sistema'),
                ('Gestão de Telas', '/oizap/gestao-telas', 'Gerenciar telas disponíveis no sistema'),
                ('Perfis x Telas', '/oizap/gestao-perfis-telas', 'Associar perfis às telas com permissões'),
                ('Usuários x Perfis', '/oizap/gestao-usuarios-perfis', 'Associar usuários aos perfis de acesso')
            ON CONFLICT (ds_rota) DO NOTHING`,
            
            // Associar perfil administrativo às telas (com todas as permissões)
            `INSERT INTO perfis_telas (cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir)
            SELECT 
                p.cd_perfil,
                t.cd_tela,
                true,
                true,
                true,
                true
            FROM perfis p
            CROSS JOIN telas t
            WHERE p.nm_perfil = 'Administrador'
            AND t.ds_rota IN ('/oizap/gestao-perfis', '/oizap/gestao-telas', '/oizap/gestao-perfis-telas', '/oizap/gestao-usuarios-perfis')
            ON CONFLICT (cd_perfil, cd_tela) DO NOTHING`,
            
            // Associar usuário 87 (Robson) ao perfil administrativo
            `INSERT INTO usuarios_perfis (cd_usuario, cd_perfil)
            SELECT 87, cd_perfil
            FROM perfis
            WHERE nm_perfil = 'Administrador'
            ON CONFLICT (cd_usuario, cd_perfil) DO NOTHING`
        ];
        
        const resultados = [];
        
        for (const query of queries) {
            try {
                const resultado = await db.query(query);
                resultados.push({ 
                    query: query.substring(0, 100) + '...', 
                    sucesso: true,
                    linhas_afetadas: resultado.data?.length || 0
                });
            } catch (error: any) {
                resultados.push({ 
                    query: query.substring(0, 100) + '...', 
                    sucesso: false, 
                    erro: error.message 
                });
            }
        }
        
        return res.status(200).send(sucesso(resultados, 'Inserção de dados iniciais concluída'));
        
    } catch (error: any) {
        console.error('Erro ao inserir dados iniciais:', error);
        return res.status(500).send(erroInterno(error));
    }
});

export { router as DebugRoutes };
