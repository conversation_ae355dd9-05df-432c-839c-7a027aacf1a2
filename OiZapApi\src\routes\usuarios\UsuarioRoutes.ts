import { Router } from 'express';
import auth from '../../middleware/authApi'; // auth caso queira autenticar

import { UsuarioController } from '../../controllers/usuarios/UsuarioController';
export const UsuarioRoutes = Router();

/* rotas */
const rotaPadrao = '/admin/usuario';

UsuarioRoutes.post(rotaPadrao + '/login/v1', UsuarioController.login);
UsuarioRoutes.get(rotaPadrao + '/listaUsuarios/v1', auth, UsuarioController.listaUsuarios);
UsuarioRoutes.get(rotaPadrao + '/listaUsuariosTodos/v1', auth, UsuarioController.listaUsuariosTodos);
UsuarioRoutes.get(
  rotaPadrao + '/listaUsuariosPorDepartamento/v1',
  auth,
  UsuarioController.listaUsuariosPorDepartamento,
);
UsuarioRoutes.get(rotaPadrao + '/listaUsuariosDepartamentos/v1', auth, UsuarioController.listaUsuariosDepartamentos);
UsuarioRoutes.post(rotaPadrao + '/v1', auth, UsuarioController.incluirUsuario);
UsuarioRoutes.put(rotaPadrao + '/v1', auth, UsuarioController.alterarUsuario);
UsuarioRoutes.put(rotaPadrao + '/alteraSenha/v1', auth, UsuarioController.alteraSenha);

// Rota para obter menu por usuário (sem autenticação para permitir acesso durante login)
UsuarioRoutes.get('/usuarios/:cd_usuario/menu/v1', UsuarioController.obterMenuPorUsuario);
//UsuarioRoutes.get(rotaPadrao + '/v1', auth, UsuarioController.listaUsuarios);
//UsuarioRoutes.get(rotaPadrao + '/v1', auth, UsuarioController.listarSprint);
//UsuarioRoutes.get(rotaPadrao + '/listar/v1', auth, UsuarioController.listarSprintSQL);
//UsuarioRoutes.delete(rotaPadrao + '/v1', auth, UsuarioController.removerSprint);

/* end rotas */
