<template>
  <div class="gestao-modulos">
    <!-- Header -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="fas fa-cubes"></i>
        Gestão de Módulos e Telas
      </h1>
      <p class="page-description">
        Gerencie módulos do sistema e suas telas associadas
      </p>
    </div>

    <!-- Tabs -->
    <div class="tabs-container">
      <div class="tabs">
        <button 
          class="tab-button" 
          :class="{ active: activeTab === 'modulos' }"
          @click="activeTab = 'modulos'"
        >
          <i class="fas fa-cubes"></i>
          Módulos
        </button>
        <button 
          class="tab-button" 
          :class="{ active: activeTab === 'telas' }"
          @click="activeTab = 'telas'"
        >
          <i class="fas fa-window-maximize"></i>
          Telas por Módulo
        </button>
      </div>
    </div>

    <!-- Tab Content: <PERSON><PERSON><PERSON><PERSON> -->
    <div v-if="activeTab === 'modulos'" class="tab-content">
      <div class="content-header">
        <div class="search-container">
          <input
            v-model="filtroModulos"
            type="text"
            placeholder="Buscar módulos..."
            class="search-input"
          >
        </div>
        <button @click="abrirModalModulo('create')" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          Novo Módulo
        </button>
      </div>

      <!-- Lista de Módulos -->
      <div class="modulos-grid">
        <div 
          v-for="modulo in modulosFiltrados" 
          :key="modulo.cd_modulo"
          class="modulo-card"
        >
          <div class="modulo-header">
            <h3>{{ modulo.nm_modulo }}</h3>
            <div class="modulo-actions">
              <button @click="abrirModalModulo('edit', modulo)" class="btn btn-sm btn-outline">
                <i class="fas fa-edit"></i>
              </button>
              <button @click="deletarModulo(modulo)" class="btn btn-sm btn-danger">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          <p class="modulo-description">{{ modulo.ds_funcionalidade }}</p>
          <div class="modulo-stats">
            <span class="stat">
              <i class="fas fa-window-maximize"></i>
              {{ contarTelasPorModulo(modulo.cd_modulo) }} telas
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Content: Telas por Módulo -->
    <div v-if="activeTab === 'telas'" class="tab-content">
      <div class="content-header">
        <div class="search-container">
          <input
            v-model="filtroTelas"
            type="text"
            placeholder="Buscar telas..."
            class="search-input"
          >
        </div>
        <button @click="abrirModalTela('create')" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          Nova Tela
        </button>
      </div>

      <!-- Telas Agrupadas por Módulo -->
      <div class="modulos-telas-container">
        <div 
          v-for="modulo in telasAgrupadasFiltradas" 
          :key="modulo.cd_modulo"
          class="modulo-section"
        >
          <div class="modulo-section-header">
            <h3>
              <i class="fas fa-cube"></i>
              {{ modulo.nm_modulo }}
            </h3>
            <span class="telas-count">{{ modulo.telas.length }} telas</span>
          </div>

          <div class="telas-grid">
            <div 
              v-for="tela in modulo.telas" 
              :key="tela.cd_tela"
              class="tela-card"
            >
              <div class="tela-header">
                <h4>{{ tela.nm_tela }}</h4>
                <div class="tela-actions">
                  <button @click="abrirModalTela('edit', tela)" class="btn btn-sm btn-outline">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button @click="deletarTela(tela)" class="btn btn-sm btn-danger">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
              <p class="tela-route">{{ tela.ds_rota }}</p>
              <p class="tela-description">{{ tela.ds_descricao }}</p>
              <div class="tela-stats">
                <span class="stat">
                  <i class="fas fa-users"></i>
                  {{ tela.total_perfis || 0 }} perfis
                </span>
                <span class="status" :class="{ active: tela.in_ativa }">
                  {{ tela.in_ativa ? 'Ativa' : 'Inativa' }}
                </span>
              </div>
            </div>

            <!-- Card para adicionar nova tela ao módulo -->
            <div class="tela-card add-tela-card" @click="abrirModalTela('create', null, modulo.cd_modulo)">
              <div class="add-content">
                <i class="fas fa-plus"></i>
                <span>Adicionar Tela</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Módulo -->
    <div v-if="showModuloModal" class="modal-overlay" @click="fecharModalModulo">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ modalModuloTitle }}</h3>
          <button @click="fecharModalModulo" class="btn-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="salvarModulo">
            <div class="form-group">
              <label for="nm_modulo">Nome do Módulo *</label>
              <input
                id="nm_modulo"
                v-model="formModulo.nm_modulo"
                type="text"
                class="form-control"
                required
              >
            </div>
            <div class="form-group">
              <label for="ds_funcionalidade">Descrição</label>
              <textarea
                id="ds_funcionalidade"
                v-model="formModulo.ds_funcionalidade"
                class="form-control"
                rows="3"
              ></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button @click="fecharModalModulo" class="btn btn-secondary">Cancelar</button>
          <button @click="salvarModulo" class="btn btn-primary" :disabled="salvando">
            <i v-if="salvando" class="fas fa-spinner fa-spin"></i>
            {{ salvando ? 'Salvando...' : 'Salvar' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Modal Tela -->
    <div v-if="showTelaModal" class="modal-overlay" @click="fecharModalTela">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ modalTelaTitle }}</h3>
          <button @click="fecharModalTela" class="btn-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="salvarTela">
            <div class="form-group">
              <label for="cd_modulo_tela">Módulo *</label>
              <select
                id="cd_modulo_tela"
                v-model="formTela.cd_modulo"
                class="form-control"
                required
              >
                <option value="">Selecione um módulo</option>
                <option 
                  v-for="modulo in modulos" 
                  :key="modulo.cd_modulo" 
                  :value="modulo.cd_modulo"
                >
                  {{ modulo.nm_modulo }}
                </option>
              </select>
            </div>
            <div class="form-group">
              <label for="nm_tela">Nome da Tela *</label>
              <input
                id="nm_tela"
                v-model="formTela.nm_tela"
                type="text"
                class="form-control"
                required
              >
            </div>
            <div class="form-group">
              <label for="ds_rota">Rota *</label>
              <input
                id="ds_rota"
                v-model="formTela.ds_rota"
                type="text"
                class="form-control"
                placeholder="/exemplo-rota"
                required
              >
            </div>
            <div class="form-group">
              <label for="ds_descricao_tela">Descrição</label>
              <textarea
                id="ds_descricao_tela"
                v-model="formTela.ds_descricao"
                class="form-control"
                rows="3"
              ></textarea>
            </div>
            <div class="form-group">
              <label class="checkbox-label">
                <input
                  v-model="formTela.in_ativa"
                  type="checkbox"
                >
                Tela Ativa
              </label>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button @click="fecharModalTela" class="btn btn-secondary">Cancelar</button>
          <button @click="salvarTela" class="btn btn-primary" :disabled="salvando">
            <i v-if="salvando" class="fas fa-spinner fa-spin"></i>
            {{ salvando ? 'Salvando...' : 'Salvar' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Carregando...</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import AccessControlService from '@/services/administracao/AccessControlService'

const toast = useToast()

// Estados
const activeTab = ref('modulos')
const loading = ref(false)
const salvando = ref(false)

// Dados
const modulos = ref([])
const telasAgrupadasPorModulo = ref([])

// Filtros
const filtroModulos = ref('')
const filtroTelas = ref('')

// Modais
const showModuloModal = ref(false)
const showTelaModal = ref(false)
const modalModuloMode = ref('create')
const modalTelaMode = ref('create')

// Formulários
const formModulo = ref({
  nm_modulo: '',
  ds_funcionalidade: ''
})

const formTela = ref({
  cd_modulo: '',
  nm_tela: '',
  ds_rota: '',
  ds_descricao: '',
  in_ativa: true
})

// Computed
const modalModuloTitle = computed(() => {
  return modalModuloMode.value === 'create' ? 'Novo Módulo' : 'Editar Módulo'
})

const modalTelaTitle = computed(() => {
  return modalTelaMode.value === 'create' ? 'Nova Tela' : 'Editar Tela'
})

const modulosFiltrados = computed(() => {
  if (!filtroModulos.value) return modulos.value
  const termo = filtroModulos.value.toLowerCase()
  return modulos.value.filter(modulo => 
    modulo.nm_modulo.toLowerCase().includes(termo) ||
    modulo.ds_funcionalidade?.toLowerCase().includes(termo)
  )
})

const telasAgrupadasFiltradas = computed(() => {
  if (!filtroTelas.value) return telasAgrupadasPorModulo.value
  const termo = filtroTelas.value.toLowerCase()
  return telasAgrupadasPorModulo.value.map(modulo => ({
    ...modulo,
    telas: modulo.telas.filter(tela =>
      tela.nm_tela.toLowerCase().includes(termo) ||
      tela.ds_rota?.toLowerCase().includes(termo) ||
      tela.ds_descricao?.toLowerCase().includes(termo)
    )
  })).filter(modulo => modulo.telas.length > 0)
})

// Métodos
const contarTelasPorModulo = (cd_modulo) => {
  const modulo = telasAgrupadasPorModulo.value.find(m => m.cd_modulo === cd_modulo)
  return modulo ? modulo.telas.length : 0
}

const carregarDados = async () => {
  loading.value = true
  try {
    await Promise.all([
      carregarModulos(),
      carregarTelasAgrupadasPorModulo()
    ])
  } catch (error) {
    console.error('Erro ao carregar dados:', error)
    toast.error('Erro ao carregar dados')
  } finally {
    loading.value = false
  }
}

const carregarModulos = async () => {
  try {
    const response = await AccessControlService.listarModulos()
    if (response.statuscode === 200) {
      modulos.value = response.data || []
    }
  } catch (error) {
    console.error('Erro ao carregar módulos:', error)
    throw error
  }
}

const carregarTelasAgrupadasPorModulo = async () => {
  try {
    const response = await AccessControlService.listarTelasAgrupadasPorModulo()
    if (response.statuscode === 200) {
      telasAgrupadasPorModulo.value = response.data || []
    }
  } catch (error) {
    console.error('Erro ao carregar telas agrupadas:', error)
    throw error
  }
}

// Módulos
const abrirModalModulo = (mode, modulo = null) => {
  modalModuloMode.value = mode
  if (mode === 'edit' && modulo) {
    formModulo.value = { ...modulo }
  } else {
    formModulo.value = {
      nm_modulo: '',
      ds_funcionalidade: ''
    }
  }
  showModuloModal.value = true
}

const fecharModalModulo = () => {
  showModuloModal.value = false
  formModulo.value = {
    nm_modulo: '',
    ds_funcionalidade: ''
  }
}

const salvarModulo = async () => {
  salvando.value = true
  try {
    let response
    if (modalModuloMode.value === 'create') {
      response = await AccessControlService.criarModulo(formModulo.value)
    } else {
      response = await AccessControlService.atualizarModulo(formModulo.value.cd_modulo, formModulo.value)
    }

    if (response.statuscode === 200 || response.statuscode === 201) {
      toast.success(modalModuloMode.value === 'create' ? 'Módulo criado com sucesso!' : 'Módulo atualizado com sucesso!')
      fecharModalModulo()
      await carregarDados()
    } else {
      toast.error(response.message || 'Erro ao salvar módulo')
    }
  } catch (error) {
    console.error('Erro ao salvar módulo:', error)
    toast.error('Erro ao salvar módulo')
  } finally {
    salvando.value = false
  }
}

const deletarModulo = async (modulo) => {
  if (!confirm(`Tem certeza que deseja deletar o módulo "${modulo.nm_modulo}"?`)) {
    return
  }

  try {
    const response = await AccessControlService.deletarModulo(modulo.cd_modulo)
    if (response.statuscode === 200) {
      toast.success('Módulo deletado com sucesso!')
      await carregarDados()
    } else {
      toast.error(response.message || 'Erro ao deletar módulo')
    }
  } catch (error) {
    console.error('Erro ao deletar módulo:', error)
    toast.error('Erro ao deletar módulo')
  }
}

// Telas
const abrirModalTela = (mode, tela = null, cd_modulo = null) => {
  modalTelaMode.value = mode
  if (mode === 'edit' && tela) {
    formTela.value = { ...tela }
  } else {
    formTela.value = {
      cd_modulo: cd_modulo || '',
      nm_tela: '',
      ds_rota: '',
      ds_descricao: '',
      in_ativa: true
    }
  }
  showTelaModal.value = true
}

const fecharModalTela = () => {
  showTelaModal.value = false
  formTela.value = {
    cd_modulo: '',
    nm_tela: '',
    ds_rota: '',
    ds_descricao: '',
    in_ativa: true
  }
}

const salvarTela = async () => {
  salvando.value = true
  try {
    let response
    if (modalTelaMode.value === 'create') {
      response = await AccessControlService.criarTela(formTela.value)
    } else {
      response = await AccessControlService.atualizarTela(formTela.value.cd_tela, formTela.value)
    }

    if (response.statuscode === 200 || response.statuscode === 201) {
      toast.success(modalTelaMode.value === 'create' ? 'Tela criada com sucesso!' : 'Tela atualizada com sucesso!')
      fecharModalTela()
      await carregarDados()
    } else {
      toast.error(response.message || 'Erro ao salvar tela')
    }
  } catch (error) {
    console.error('Erro ao salvar tela:', error)
    toast.error('Erro ao salvar tela')
  } finally {
    salvando.value = false
  }
}

const deletarTela = async (tela) => {
  if (!confirm(`Tem certeza que deseja deletar a tela "${tela.nm_tela}"?`)) {
    return
  }

  try {
    const response = await AccessControlService.deletarTela(tela.cd_tela)
    if (response.statuscode === 200) {
      toast.success('Tela deletada com sucesso!')
      await carregarDados()
    } else {
      toast.error(response.message || 'Erro ao deletar tela')
    }
  } catch (error) {
    console.error('Erro ao deletar tela:', error)
    toast.error('Erro ao deletar tela')
  }
}

// Lifecycle
onMounted(async () => {
  await carregarDados()
})
</script>

<style scoped>
.gestao-modulos {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
}

.tabs-container {
  margin-bottom: 30px;
}

.tabs {
  display: flex;
  border-bottom: 2px solid #ecf0f1;
}

.tab-button {
  padding: 12px 24px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: #7f8c8d;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-button:hover {
  color: #3498db;
}

.tab-button.active {
  color: #3498db;
  border-bottom-color: #3498db;
}

.tab-content {
  min-height: 400px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.search-container {
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 10px 16px;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.btn-outline {
  background: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background: #c0392b;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.modulos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.modulo-card {
  background: white;
  border: 1px solid #ecf0f1;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.modulo-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.modulo-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.modulo-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.modulo-actions {
  display: flex;
  gap: 8px;
}

.modulo-description {
  color: #7f8c8d;
  margin-bottom: 16px;
  line-height: 1.5;
}

.modulo-stats {
  display: flex;
  gap: 16px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #7f8c8d;
}

.modulo-section {
  margin-bottom: 32px;
}

.modulo-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #ecf0f1;
}

.modulo-section-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
}

.telas-count {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.telas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.tela-card {
  background: white;
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.tela-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.add-tela-card {
  border: 2px dashed #bdc3c7;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  min-height: 120px;
}

.add-tela-card:hover {
  border-color: #3498db;
  background: #f8f9fa;
}

.add-content {
  text-align: center;
  color: #7f8c8d;
}

.add-content i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

.tela-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.tela-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.tela-actions {
  display: flex;
  gap: 6px;
}

.tela-route {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #e74c3c;
  margin-bottom: 8px;
}

.tela-description {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.4;
}

.tela-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: #e74c3c;
  color: white;
}

.status.active {
  background: #27ae60;
}

/* Modais */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #ecf0f1;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.btn-close {
  background: none;
  border: none;
  font-size: 18px;
  color: #7f8c8d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-close:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #ecf0f1;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #ecf0f1;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
}

/* Loading */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  text-align: center;
  color: #3498db;
}

.loading-spinner i {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

/* Responsivo */
@media (max-width: 768px) {
  .gestao-modulos {
    padding: 16px;
  }

  .content-header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
    margin-bottom: 16px;
  }

  .modulos-grid,
  .telas-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }
}
</style>
</script>
