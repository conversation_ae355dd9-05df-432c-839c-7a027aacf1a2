<template>
  <div class="p-5 max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-semibold text-gray-800 mb-2 flex items-center gap-3">
        <i class="fas fa-cubes text-blue-500"></i>
        Gestão de Módulos e Telas
      </h1>
      <p class="text-gray-600 text-base">
        Gerencie módulos do sistema e suas telas associadas
      </p>
    </div>

    <!-- Tabs -->
    <div class="mb-8">
      <div class="flex border-b-2 border-gray-200">
        <button
          class="px-6 py-3 border-b-3 border-transparent text-gray-500 font-medium text-base transition-all duration-300 flex items-center gap-2 hover:text-blue-500"
          :class="{ 'text-blue-500 border-blue-500': activeTab === 'modulos' }"
          @click="activeTab = 'modulos'"
        >
          <i class="fas fa-cubes"></i>
          M<PERSON><PERSON><PERSON>
        </button>
        <button
          class="px-6 py-3 border-b-3 border-transparent text-gray-500 font-medium text-base transition-all duration-300 flex items-center gap-2 hover:text-blue-500"
          :class="{ 'text-blue-500 border-blue-500': activeTab === 'telas' }"
          @click="activeTab = 'telas'"
        >
          <i class="fas fa-window-maximize"></i>
          Telas por Módulo
        </button>
      </div>
    </div>

    <!-- Tab Content: Módulos -->
    <div v-if="activeTab === 'modulos'" class="min-h-96">
      <div class="flex justify-between items-center mb-6 gap-4">
        <div class="flex-1 max-w-md">
          <input
            v-model="filtroModulos"
            type="text"
            placeholder="Buscar módulos..."
            class="w-full px-4 py-2.5 border-2 border-gray-200 rounded-lg text-sm transition-colors duration-300 focus:outline-none focus:border-blue-500"
          >
        </div>
        <button @click="abrirModalModulo('create')" class="px-5 py-2.5 bg-blue-500 text-white rounded-lg text-sm font-medium transition-all duration-300 flex items-center gap-2 hover:bg-blue-600">
          <i class="fas fa-plus"></i>
          Novo Módulo
        </button>
      </div>

      <!-- Lista de Módulos -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
        <div
          v-for="modulo in modulosFiltrados"
          :key="modulo.cd_modulo"
          class="bg-white border border-gray-200 rounded-xl p-5 transition-all duration-300 shadow-sm hover:-translate-y-0.5 hover:shadow-md"
        >
          <div class="flex justify-between items-start mb-3">
            <h3 class="text-lg font-semibold text-gray-800 m-0">{{ modulo.nm_modulo }}</h3>
            <div class="flex gap-2">
              <button @click="abrirModalModulo('edit', modulo)" class="px-3 py-1.5 text-xs bg-transparent text-blue-500 border border-blue-500 rounded transition-all duration-300 hover:bg-blue-500 hover:text-white">
                <i class="fas fa-edit"></i>
              </button>
              <button @click="deletarModulo(modulo)" class="px-3 py-1.5 text-xs bg-red-500 text-white rounded transition-all duration-300 hover:bg-red-600">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          <p class="text-gray-600 mb-4 leading-relaxed">{{ modulo.ds_funcionalidade }}</p>
          <div class="flex gap-4">
            <span class="flex items-center gap-1.5 text-sm text-gray-600">
              <i class="fas fa-window-maximize"></i>
              {{ contarTelasPorModulo(modulo.cd_modulo) }} telas
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Content: Telas por Módulo -->
    <div v-if="activeTab === 'telas'" class="min-h-96">
      <div class="flex justify-between items-center mb-6 gap-4">
        <div class="flex-1 max-w-md">
          <input
            v-model="filtroTelas"
            type="text"
            placeholder="Buscar telas..."
            class="w-full px-4 py-2.5 border-2 border-gray-200 rounded-lg text-sm transition-colors duration-300 focus:outline-none focus:border-blue-500"
          >
        </div>
        <button @click="abrirModalTela('create')" class="px-5 py-2.5 bg-blue-500 text-white rounded-lg text-sm font-medium transition-all duration-300 flex items-center gap-2 hover:bg-blue-600">
          <i class="fas fa-plus"></i>
          Nova Tela
        </button>
      </div>

      <!-- Telas Agrupadas por Módulo -->
      <div class="space-y-8">
        <div
          v-for="modulo in telasAgrupadasFiltradas"
          :key="modulo.cd_modulo"
          class="mb-8"
        >
          <div class="flex justify-between items-center mb-4 pb-3 border-b-2 border-gray-200">
            <h3 class="text-xl font-semibold text-gray-800 m-0 flex items-center gap-2.5">
              <i class="fas fa-cube text-blue-500"></i>
              {{ modulo.nm_modulo }}
            </h3>
            <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">{{ modulo.telas.length }} telas</span>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <div
              v-for="tela in modulo.telas"
              :key="tela.cd_tela"
              class="bg-white border border-gray-200 rounded-lg p-4 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md"
            >
              <div class="flex justify-between items-start mb-2">
                <h4 class="text-base font-semibold text-gray-800 m-0">{{ tela.nm_tela }}</h4>
                <div class="flex gap-1.5">
                  <button @click="abrirModalTela('edit', tela)" class="px-2 py-1 text-xs bg-transparent text-blue-500 border border-blue-500 rounded transition-all duration-300 hover:bg-blue-500 hover:text-white">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button @click="deletarTela(tela)" class="px-2 py-1 text-xs bg-red-500 text-white rounded transition-all duration-300 hover:bg-red-600">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
              <p class="font-mono bg-gray-100 px-2 py-1 rounded text-xs text-red-500 mb-2">{{ tela.ds_rota }}</p>
              <p class="text-gray-600 text-sm mb-3 leading-relaxed">{{ tela.ds_descricao }}</p>
              <div class="flex justify-between items-center">
                <span class="flex items-center gap-1.5 text-sm text-gray-600">
                  <i class="fas fa-users"></i>
                  {{ tela.total_perfis || 0 }} perfis
                </span>
                <span class="px-2 py-0.5 rounded-xl text-xs font-medium" :class="tela.in_ativa ? 'bg-green-500 text-white' : 'bg-red-500 text-white'">
                  {{ tela.in_ativa ? 'Ativa' : 'Inativa' }}
                </span>
              </div>
            </div>

            <!-- Card para adicionar nova tela ao módulo -->
            <div class="bg-white border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center cursor-pointer transition-all duration-300 hover:border-blue-500 hover:bg-gray-50 min-h-30" @click="abrirModalTela('create', null, modulo.cd_modulo)">
              <div class="text-center text-gray-500">
                <i class="fas fa-plus text-2xl mb-2 block"></i>
                <span class="text-sm">Adicionar Tela</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Módulo -->
    <div v-if="showModuloModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="fecharModalModulo">
      <div class="bg-white rounded-xl w-11/12 max-w-lg max-h-screen overflow-y-auto shadow-2xl" @click.stop>
        <div class="flex justify-between items-center p-5 border-b border-gray-200">
          <h3 class="text-xl font-semibold text-gray-800 m-0">{{ modalModuloTitle }}</h3>
          <button @click="fecharModalModulo" class="bg-transparent border-0 text-lg text-gray-500 cursor-pointer p-1 rounded transition-all duration-300 hover:bg-gray-200 hover:text-gray-800">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="p-6">
          <form @submit.prevent="salvarModulo">
            <div class="mb-5">
              <label for="nm_modulo" class="block mb-1.5 font-medium text-gray-800">Nome do Módulo *</label>
              <input
                id="nm_modulo"
                v-model="formModulo.nm_modulo"
                type="text"
                class="w-full px-3 py-2.5 border-2 border-gray-200 rounded-lg text-sm transition-colors duration-300 focus:outline-none focus:border-blue-500 box-border"
                required
              >
            </div>
            <div class="mb-5">
              <label for="ds_funcionalidade" class="block mb-1.5 font-medium text-gray-800">Descrição</label>
              <textarea
                id="ds_funcionalidade"
                v-model="formModulo.ds_funcionalidade"
                class="w-full px-3 py-2.5 border-2 border-gray-200 rounded-lg text-sm transition-colors duration-300 focus:outline-none focus:border-blue-500 box-border"
                rows="3"
              ></textarea>
            </div>
          </form>
        </div>
        <div class="flex justify-end gap-3 p-5 border-t border-gray-200">
          <button @click="fecharModalModulo" class="px-5 py-2.5 bg-gray-500 text-white rounded-lg text-sm font-medium transition-all duration-300 hover:bg-gray-600">Cancelar</button>
          <button @click="salvarModulo" class="px-5 py-2.5 bg-blue-500 text-white rounded-lg text-sm font-medium transition-all duration-300 flex items-center gap-2 hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed" :disabled="salvando">
            <i v-if="salvando" class="fas fa-spinner fa-spin"></i>
            {{ salvando ? 'Salvando...' : 'Salvar' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Modal Tela -->
    <div v-if="showTelaModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="fecharModalTela">
      <div class="bg-white rounded-xl w-11/12 max-w-lg max-h-screen overflow-y-auto shadow-2xl" @click.stop>
        <div class="flex justify-between items-center p-5 border-b border-gray-200">
          <h3 class="text-xl font-semibold text-gray-800 m-0">{{ modalTelaTitle }}</h3>
          <button @click="fecharModalTela" class="bg-transparent border-0 text-lg text-gray-500 cursor-pointer p-1 rounded transition-all duration-300 hover:bg-gray-200 hover:text-gray-800">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="p-6">
          <form @submit.prevent="salvarTela">
            <div class="mb-5">
              <label for="cd_modulo_tela" class="block mb-1.5 font-medium text-gray-800">Módulo *</label>
              <select
                id="cd_modulo_tela"
                v-model="formTela.cd_modulo"
                class="w-full px-3 py-2.5 border-2 border-gray-200 rounded-lg text-sm transition-colors duration-300 focus:outline-none focus:border-blue-500 box-border"
                required
              >
                <option value="">Selecione um módulo</option>
                <option
                  v-for="modulo in modulos"
                  :key="modulo.cd_modulo"
                  :value="modulo.cd_modulo"
                >
                  {{ modulo.nm_modulo }}
                </option>
              </select>
            </div>
            <div class="mb-5">
              <label for="nm_tela" class="block mb-1.5 font-medium text-gray-800">Nome da Tela *</label>
              <input
                id="nm_tela"
                v-model="formTela.nm_tela"
                type="text"
                class="w-full px-3 py-2.5 border-2 border-gray-200 rounded-lg text-sm transition-colors duration-300 focus:outline-none focus:border-blue-500 box-border"
                required
              >
            </div>
            <div class="mb-5">
              <label for="ds_rota" class="block mb-1.5 font-medium text-gray-800">Rota *</label>
              <input
                id="ds_rota"
                v-model="formTela.ds_rota"
                type="text"
                class="w-full px-3 py-2.5 border-2 border-gray-200 rounded-lg text-sm transition-colors duration-300 focus:outline-none focus:border-blue-500 box-border"
                placeholder="/exemplo-rota"
                required
              >
            </div>
            <div class="mb-5">
              <label for="ds_descricao_tela" class="block mb-1.5 font-medium text-gray-800">Descrição</label>
              <textarea
                id="ds_descricao_tela"
                v-model="formTela.ds_descricao"
                class="w-full px-3 py-2.5 border-2 border-gray-200 rounded-lg text-sm transition-colors duration-300 focus:outline-none focus:border-blue-500 box-border"
                rows="3"
              ></textarea>
            </div>
            <div class="mb-5">
              <label class="flex items-center gap-2 cursor-pointer">
                <input
                  v-model="formTela.in_ativa"
                  type="checkbox"
                  class="w-auto"
                >
                <span class="text-gray-800 font-medium">Tela Ativa</span>
              </label>
            </div>
          </form>
        </div>
        <div class="flex justify-end gap-3 p-5 border-t border-gray-200">
          <button @click="fecharModalTela" class="px-5 py-2.5 bg-gray-500 text-white rounded-lg text-sm font-medium transition-all duration-300 hover:bg-gray-600">Cancelar</button>
          <button @click="salvarTela" class="px-5 py-2.5 bg-blue-500 text-white rounded-lg text-sm font-medium transition-all duration-300 flex items-center gap-2 hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed" :disabled="salvando">
            <i v-if="salvando" class="fas fa-spinner fa-spin"></i>
            {{ salvando ? 'Salvando...' : 'Salvar' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div v-if="loading" class="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
      <div class="text-center text-blue-500">
        <i class="fas fa-spinner fa-spin text-4xl mb-3 block"></i>
        <span class="text-lg">Carregando...</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useToast } from '@/global-components/toastify/useToast'
import AccessControlService from '@/services/administracao/AccessControlService'

const toast = useToast()

// Estados
const activeTab = ref('modulos')
const loading = ref(false)
const salvando = ref(false)

// Dados
const modulos = ref([])
const telasAgrupadasPorModulo = ref([])

// Filtros
const filtroModulos = ref('')
const filtroTelas = ref('')

// Modais
const showModuloModal = ref(false)
const showTelaModal = ref(false)
const modalModuloMode = ref('create')
const modalTelaMode = ref('create')

// Formulários
const formModulo = ref({
  nm_modulo: '',
  ds_funcionalidade: ''
})

const formTela = ref({
  cd_modulo: '',
  nm_tela: '',
  ds_rota: '',
  ds_descricao: '',
  in_ativa: true
})

// Computed
const modalModuloTitle = computed(() => {
  return modalModuloMode.value === 'create' ? 'Novo Módulo' : 'Editar Módulo'
})

const modalTelaTitle = computed(() => {
  return modalTelaMode.value === 'create' ? 'Nova Tela' : 'Editar Tela'
})

const modulosFiltrados = computed(() => {
  if (!filtroModulos.value) return modulos.value
  const termo = filtroModulos.value.toLowerCase()
  return modulos.value.filter(modulo => 
    modulo.nm_modulo.toLowerCase().includes(termo) ||
    modulo.ds_funcionalidade?.toLowerCase().includes(termo)
  )
})

const telasAgrupadasFiltradas = computed(() => {
  if (!filtroTelas.value) return telasAgrupadasPorModulo.value
  const termo = filtroTelas.value.toLowerCase()
  return telasAgrupadasPorModulo.value.map(modulo => ({
    ...modulo,
    telas: modulo.telas.filter(tela =>
      tela.nm_tela.toLowerCase().includes(termo) ||
      tela.ds_rota?.toLowerCase().includes(termo) ||
      tela.ds_descricao?.toLowerCase().includes(termo)
    )
  })).filter(modulo => modulo.telas.length > 0)
})

// Métodos
const contarTelasPorModulo = (cd_modulo) => {
  const modulo = telasAgrupadasPorModulo.value.find(m => m.cd_modulo === cd_modulo)
  return modulo ? modulo.telas.length : 0
}

const carregarDados = async () => {
  loading.value = true
  try {
    await Promise.all([
      carregarModulos(),
      carregarTelasAgrupadasPorModulo()
    ])
  } catch (error) {
    console.error('Erro ao carregar dados:', error)
    toast.error('Erro ao carregar dados')
  } finally {
    loading.value = false
  }
}

const carregarModulos = async () => {
  try {
    const response = await AccessControlService.listarModulos()
    if (response.statuscode === 200) {
      modulos.value = response.data || []
    }
  } catch (error) {
    console.error('Erro ao carregar módulos:', error)
    throw error
  }
}

const carregarTelasAgrupadasPorModulo = async () => {
  try {
    const response = await AccessControlService.listarTelasAgrupadasPorModulo()
    if (response.statuscode === 200) {
      telasAgrupadasPorModulo.value = response.data || []
    }
  } catch (error) {
    console.error('Erro ao carregar telas agrupadas:', error)
    throw error
  }
}

// Módulos
const abrirModalModulo = (mode, modulo = null) => {
  modalModuloMode.value = mode
  if (mode === 'edit' && modulo) {
    formModulo.value = { ...modulo }
  } else {
    formModulo.value = {
      nm_modulo: '',
      ds_funcionalidade: ''
    }
  }
  showModuloModal.value = true
}

const fecharModalModulo = () => {
  showModuloModal.value = false
  formModulo.value = {
    nm_modulo: '',
    ds_funcionalidade: ''
  }
}

const salvarModulo = async () => {
  salvando.value = true
  try {
    let response
    if (modalModuloMode.value === 'create') {
      response = await AccessControlService.criarModulo(formModulo.value)
    } else {
      response = await AccessControlService.atualizarModulo(formModulo.value.cd_modulo, formModulo.value)
    }

    if (response.statuscode === 200 || response.statuscode === 201) {
      toast.success(modalModuloMode.value === 'create' ? 'Módulo criado com sucesso!' : 'Módulo atualizado com sucesso!')
      fecharModalModulo()
      await carregarDados()
    } else {
      toast.error(response.message || 'Erro ao salvar módulo')
    }
  } catch (error) {
    console.error('Erro ao salvar módulo:', error)
    toast.error('Erro ao salvar módulo')
  } finally {
    salvando.value = false
  }
}

const deletarModulo = async (modulo) => {
  if (!confirm(`Tem certeza que deseja deletar o módulo "${modulo.nm_modulo}"?`)) {
    return
  }

  try {
    const response = await AccessControlService.deletarModulo(modulo.cd_modulo)
    if (response.statuscode === 200) {
      toast.success('Módulo deletado com sucesso!')
      await carregarDados()
    } else {
      toast.error(response.message || 'Erro ao deletar módulo')
    }
  } catch (error) {
    console.error('Erro ao deletar módulo:', error)
    toast.error('Erro ao deletar módulo')
  }
}

// Telas
const abrirModalTela = (mode, tela = null, cd_modulo = null) => {
  modalTelaMode.value = mode
  if (mode === 'edit' && tela) {
    formTela.value = { ...tela }
  } else {
    formTela.value = {
      cd_modulo: cd_modulo || '',
      nm_tela: '',
      ds_rota: '',
      ds_descricao: '',
      in_ativa: true
    }
  }
  showTelaModal.value = true
}

const fecharModalTela = () => {
  showTelaModal.value = false
  formTela.value = {
    cd_modulo: '',
    nm_tela: '',
    ds_rota: '',
    ds_descricao: '',
    in_ativa: true
  }
}

const salvarTela = async () => {
  salvando.value = true
  try {
    let response
    if (modalTelaMode.value === 'create') {
      response = await AccessControlService.criarTela(formTela.value)
    } else {
      response = await AccessControlService.atualizarTela(formTela.value.cd_tela, formTela.value)
    }

    if (response.statuscode === 200 || response.statuscode === 201) {
      toast.success(modalTelaMode.value === 'create' ? 'Tela criada com sucesso!' : 'Tela atualizada com sucesso!')
      fecharModalTela()
      await carregarDados()
    } else {
      toast.error(response.message || 'Erro ao salvar tela')
    }
  } catch (error) {
    console.error('Erro ao salvar tela:', error)
    toast.error('Erro ao salvar tela')
  } finally {
    salvando.value = false
  }
}

const deletarTela = async (tela) => {
  if (!confirm(`Tem certeza que deseja deletar a tela "${tela.nm_tela}"?`)) {
    return
  }

  try {
    const response = await AccessControlService.deletarTela(tela.cd_tela)
    if (response.statuscode === 200) {
      toast.success('Tela deletada com sucesso!')
      await carregarDados()
    } else {
      toast.error(response.message || 'Erro ao deletar tela')
    }
  } catch (error) {
    console.error('Erro ao deletar tela:', error)
    toast.error('Erro ao deletar tela')
  }
}

// Lifecycle
onMounted(async () => {
  await carregarDados()
})
</script>
