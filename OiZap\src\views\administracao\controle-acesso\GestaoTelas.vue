<template>
    <div class="intro-y flex flex-col sm:flex-row items-center mt-8">
        <h2 class="text-lg font-medium mr-auto">Gestão de Telas</h2>
        <div class="w-full sm:w-auto flex mt-4 sm:mt-0">
            <button @click="abrirModalCriar" class="btn btn-primary shadow-md mr-2">
                <PlusIcon class="w-4 h-4 mr-2" />
                Nova Tela
            </button>
        </div>
    </div>

    <!-- Lista de Telas -->
    <div class="intro-y grid grid-cols-12 gap-6 mt-5">
        <div class="col-span-12">
            <div class="intro-y box">
                <div class="flex flex-col sm:flex-row items-center p-5 border-b border-slate-200/60">
                    <h2 class="font-medium text-base mr-auto">Lista de Telas</h2>
                    <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                        <input
                            v-model="filtro"
                            type="text"
                            class="form-control w-full sm:w-64"
                            placeholder="Buscar tela..."
                        />
                    </div>
                </div>
                <div class="p-5">
                    <div class="overflow-x-auto">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th class="whitespace-nowrap">ID</th>
                                    <th class="whitespace-nowrap">Nome da Tela</th>
                                    <th class="whitespace-nowrap">Rota</th>
                                    <th class="whitespace-nowrap">Descrição</th>
                                    <th class="whitespace-nowrap">Status</th>
                                    <th class="whitespace-nowrap">Criado em</th>
                                    <th class="whitespace-nowrap text-center">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="tela in telasFiltradas" :key="tela.cd_tela">
                                    <td>{{ tela.cd_tela }}</td>
                                    <td class="font-medium">{{ tela.nm_tela }}</td>
                                    <td>
                                        <code class="text-xs bg-slate-100 px-2 py-1 rounded">{{ tela.ds_rota }}</code>
                                    </td>
                                    <td class="max-w-xs truncate">{{ tela.ds_descricao || '-' }}</td>
                                    <td>
                                        <span 
                                            class="badge"
                                            :class="tela.in_ativo ? 'bg-success' : 'bg-secondary'"
                                        >
                                            {{ tela.in_ativo ? 'Ativo' : 'Inativo' }}
                                        </span>
                                    </td>
                                    <td>{{ formatarData(tela.created_at) }}</td>
                                    <td class="text-center">
                                        <div class="flex justify-center items-center">
                                            <button
                                                @click="editarTela(tela)"
                                                class="btn btn-outline-secondary w-8 h-8 mr-1"
                                                title="Editar"
                                            >
                                                <EditIcon class="w-4 h-4" />
                                            </button>
                                            <button
                                                @click="excluirTela(tela)"
                                                class="btn btn-outline-danger w-8 h-8"
                                                title="Excluir"
                                            >
                                                <TrashIcon class="w-4 h-4" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Criar/Editar Tela -->
    <Modal :show="modalTela" @hidden="fecharModal">
        <ModalHeader>
            <h2 class="font-medium text-base mr-auto">
                {{ telaEditando ? 'Editar Tela' : 'Nova Tela' }}
            </h2>
        </ModalHeader>
        <ModalBody>
            <div class="grid grid-cols-12 gap-4 gap-y-3">
                <div class="col-span-12">
                    <label class="form-label">Nome da Tela *</label>
                    <input
                        v-model="formTela.nm_tela"
                        type="text"
                        class="form-control"
                        placeholder="Digite o nome da tela"
                        :class="{ 'border-danger': erros.nm_tela }"
                    />
                    <div v-if="erros.nm_tela" class="text-danger text-xs mt-1">
                        {{ erros.nm_tela }}
                    </div>
                </div>
                <div class="col-span-12">
                    <label class="form-label">Rota *</label>
                    <input
                        v-model="formTela.ds_rota"
                        type="text"
                        class="form-control"
                        placeholder="/exemplo/rota"
                        :class="{ 'border-danger': erros.ds_rota }"
                    />
                    <div v-if="erros.ds_rota" class="text-danger text-xs mt-1">
                        {{ erros.ds_rota }}
                    </div>
                    <div class="text-slate-500 text-xs mt-1">
                        Exemplo: /oizap/usuarios, /admin/relatorios
                    </div>
                </div>
                <div class="col-span-12">
                    <label class="form-label">Descrição</label>
                    <textarea
                        v-model="formTela.ds_descricao"
                        class="form-control"
                        rows="3"
                        placeholder="Descrição da funcionalidade da tela"
                    ></textarea>
                </div>
                <div class="col-span-12">
                    <div class="flex items-center space-x-3">
                        <div class="form-check form-switch">
                            <input
                                v-model="formTela.in_ativo"
                                type="checkbox"
                                class="form-check-input"
                            />
                        </div>
                        <label class="text-sm font-medium text-slate-700 dark:text-slate-300">
                            {{ formTela.in_ativo ? 'Tela Ativa' : 'Tela Inativa' }}
                        </label>
                    </div>
                </div>
            </div>
        </ModalBody>
        <ModalFooter>
            <button @click="fecharModal" class="btn btn-outline-secondary w-20 mr-1">
                Cancelar
            </button>
            <button @click="salvarTela" class="btn btn-primary w-20" :disabled="salvando">
                <LoadingIcon v-if="salvando" class="w-4 h-4 mr-2" />
                {{ telaEditando ? 'Atualizar' : 'Criar' }}
            </button>
        </ModalFooter>
    </Modal>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import AccessControlService from '@/services/administracao/AccessControlService';

// Estados
const telas = ref([]);
const filtro = ref('');
const modalTela = ref(false);
const telaEditando = ref(null);
const salvando = ref(false);
const carregando = ref(false);

// Formulário
const formTela = ref({
    nm_tela: '',
    ds_rota: '',
    ds_descricao: '',
    in_ativo: true
});

const erros = ref({});

// Computed
const telasFiltradas = computed(() => {
    if (!filtro.value) return telas.value;
    
    const termo = filtro.value.toLowerCase();
    return telas.value.filter(tela =>
        tela.nm_tela.toLowerCase().includes(termo) ||
        tela.ds_rota.toLowerCase().includes(termo) ||
        (tela.ds_descricao && tela.ds_descricao.toLowerCase().includes(termo))
    );
});

// Métodos
async function carregarTelas() {
    try {
        carregando.value = true;
        const response = await AccessControlService.listarTelas();
        
        if (response.statuscode === 200) {
            telas.value = response.data;
        }
    } catch (error) {
        console.error('Erro ao carregar telas:', error);
    } finally {
        carregando.value = false;
    }
}

function abrirModalCriar() {
    telaEditando.value = null;
    formTela.value = {
        nm_tela: '',
        ds_rota: '',
        ds_descricao: '',
        in_ativo: true
    };
    erros.value = {};
    modalTela.value = true;
}

function editarTela(tela) {
    telaEditando.value = tela;
    formTela.value = {
        nm_tela: tela.nm_tela,
        ds_rota: tela.ds_rota,
        ds_descricao: tela.ds_descricao || '',
        in_ativo: tela.in_ativo
    };
    erros.value = {};
    modalTela.value = true;
}

function fecharModal() {
    modalTela.value = false;
    telaEditando.value = null;
    formTela.value = {
        nm_tela: '',
        ds_rota: '',
        ds_descricao: '',
        in_ativo: true
    };
    erros.value = {};
}

async function salvarTela() {
    try {
        salvando.value = true;
        erros.value = {};

        // Validações
        if (!formTela.value.nm_tela?.trim()) {
            erros.value.nm_tela = 'Nome da tela é obrigatório';
            return;
        }

        if (!formTela.value.ds_rota?.trim()) {
            erros.value.ds_rota = 'Rota é obrigatória';
            return;
        }

        if (!formTela.value.ds_rota.startsWith('/')) {
            erros.value.ds_rota = 'Rota deve começar com "/"';
            return;
        }

        let response;
        
        if (telaEditando.value) {
            response = await AccessControlService.atualizarTela(
                telaEditando.value.cd_tela,
                formTela.value
            );
        } else {
            response = await AccessControlService.criarTela(formTela.value);
        }

        if (response.statuscode === 200) {
            await carregarTelas();
            fecharModal();
            // Mostrar notificação de sucesso
        } else {
            // Mostrar erro
            console.error('Erro ao salvar tela:', response.message);
        }
    } catch (error) {
        console.error('Erro ao salvar tela:', error);
    } finally {
        salvando.value = false;
    }
}

async function excluirTela(tela) {
    if (!confirm(`Tem certeza que deseja excluir a tela "${tela.nm_tela}"?`)) {
        return;
    }

    try {
        const response = await AccessControlService.deletarTela(tela.cd_tela);
        
        if (response.statuscode === 200) {
            await carregarTelas();
            // Mostrar notificação de sucesso
        } else {
            // Mostrar erro
            console.error('Erro ao excluir tela:', response.message);
        }
    } catch (error) {
        console.error('Erro ao excluir tela:', error);
    }
}

function formatarData(data) {
    if (!data) return '-';
    return new Date(data).toLocaleDateString('pt-BR');
}

// Lifecycle
onMounted(() => {
    carregarTelas();
});
</script>
