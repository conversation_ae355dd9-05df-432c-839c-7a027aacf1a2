import AccessControlService from '@/services/administracao/AccessControlService';

/**
 * Middleware para verificar permissões de acesso no frontend
 */
export class AccessControlMiddleware {
    constructor() {
        this.telasPermitidas = this.carregarTelasPermitidas();
        this.cd_usuario = this.obterUsuarioLogado();
    }

    /**
     * Carrega as telas permitidas do localStorage
     */
    carregarTelasPermitidas() {
        try {
            const telas = localStorage.getItem('telasPermitidas');
            return telas ? JSON.parse(telas) : [];
        } catch (error) {
            console.error('Erro ao carregar telas permitidas:', error);
            return [];
        }
    }

    /**
     * Obtém o código do usuário logado
     */
    obterUsuarioLogado() {
        return localStorage.getItem('codusuario');
    }

    /**
     * Verifica se o usuário tem acesso a uma rota específica
     * @param {string} rota - Rota a ser verificada
     * @param {string} permissao - Tipo de permissão (visualizar, inserir, alterar, excluir)
     * @returns {boolean}
     */
    temAcesso(rota, permissao = 'visualizar') {
        // Se não há telas carregadas, permitir acesso (fallback para sistema antigo)
        if (!this.telasPermitidas.length) {
            return true;
        }

        const telaPermitida = this.telasPermitidas.find(item => 
            item.tela && item.tela.ds_rota === rota
        );

        if (!telaPermitida) {
            return false;
        }

        const permissoes = telaPermitida.permissoes;
        
        switch (permissao) {
            case 'visualizar':
                return permissoes.in_visualizar;
            case 'inserir':
                return permissoes.in_inserir;
            case 'alterar':
                return permissoes.in_alterar;
            case 'excluir':
                return permissoes.in_excluir;
            default:
                return permissoes.in_visualizar;
        }
    }

    /**
     * Verifica se o usuário tem permissão para visualizar
     */
    podeVisualizar(rota) {
        return this.temAcesso(rota, 'visualizar');
    }

    /**
     * Verifica se o usuário tem permissão para inserir
     */
    podeInserir(rota) {
        return this.temAcesso(rota, 'inserir');
    }

    /**
     * Verifica se o usuário tem permissão para alterar
     */
    podeAlterar(rota) {
        return this.temAcesso(rota, 'alterar');
    }

    /**
     * Verifica se o usuário tem permissão para excluir
     */
    podeExcluir(rota) {
        return this.temAcesso(rota, 'excluir');
    }

    /**
     * Obtém as permissões de uma rota específica
     */
    obterPermissoes(rota) {
        const telaPermitida = this.telasPermitidas.find(item => 
            item.tela && item.tela.ds_rota === rota
        );

        if (!telaPermitida) {
            return {
                in_visualizar: false,
                in_inserir: false,
                in_alterar: false,
                in_excluir: false
            };
        }

        return telaPermitida.permissoes;
    }

    /**
     * Verifica acesso em tempo real (consulta a API)
     * @param {string} rota 
     */
    async verificarAcessoOnline(rota) {
        if (!this.cd_usuario) {
            return false;
        }

        try {
            const response = await AccessControlService.verificarAcesso(this.cd_usuario, rota);
            return response.statuscode === 200 && response.data;
        } catch (error) {
            console.error('Erro ao verificar acesso online:', error);
            return false;
        }
    }

    /**
     * Atualiza as telas permitidas (útil após mudanças de perfil)
     */
    async atualizarPermissoes() {
        if (!this.cd_usuario) {
            return;
        }

        try {
            const menuAcesso = await AccessControlService.obterMenuPorUsuario(this.cd_usuario);
            
            if (menuAcesso.telasPermitidas) {
                this.telasPermitidas = menuAcesso.telasPermitidas;
                localStorage.setItem('telasPermitidas', JSON.stringify(menuAcesso.telasPermitidas));
            }
        } catch (error) {
            console.error('Erro ao atualizar permissões:', error);
        }
    }

    /**
     * Middleware para Vue Router
     */
    static criarGuardaRota() {
        return (to, from, next) => {
            const accessControl = new AccessControlMiddleware();
            
            // Verificar se a rota requer controle de acesso
            const rotaRequerControle = to.meta?.requiresAccess;
            
            if (!rotaRequerControle) {
                next();
                return;
            }

            const rota = to.path;
            const permissaoRequerida = to.meta?.permission || 'visualizar';

            if (accessControl.temAcesso(rota, permissaoRequerida)) {
                next();
            } else {
                // Redirecionar para página de acesso negado
                next({ name: 'access-denied', query: { rota, permissao: permissaoRequerida } });
            }
        };
    }

    /**
     * Plugin para Vue 3
     */
    static criarPlugin() {
        return {
            install(app) {
                const accessControl = new AccessControlMiddleware();
                
                // Disponibilizar globalmente
                app.config.globalProperties.$accessControl = accessControl;
                
                // Provide/inject
                app.provide('accessControl', accessControl);
            }
        };
    }
}

// Instância global
export const accessControl = new AccessControlMiddleware();

// Composable para Vue 3
export function useAccessControl() {
    return accessControl;
}

// Helpers para templates
export const accessHelpers = {
    canView: (rota) => accessControl.podeVisualizar(rota),
    canCreate: (rota) => accessControl.podeInserir(rota),
    canEdit: (rota) => accessControl.podeAlterar(rota),
    canDelete: (rota) => accessControl.podeExcluir(rota),
    getPermissions: (rota) => accessControl.obterPermissoes(rota)
};
