//import UsuariosServices from '@/services/administracao/UsuariosServices';
import AccessControlService from '@/services/administracao/AccessControlService';

import { sub } from 'date-fns';

export async function obtemMenu(dados) {
    //console.log('🚀 ~ useMenu.js:6 ~ obtemMenu ~ dados:', dados);
    var menu = [];

    const findApi = dados.modulos.find((modulo) => modulo.nm_modulo == 'API');
    const findChatPedidos = dados.modulos.find((modulo) => modulo.nm_modulo == 'ChatBot/Pedidos');
    const findCRM = dados.modulos.find((modulo) => modulo.nm_modulo == 'CRM');

    if (findChatPedidos || findCRM) {
        menu.push({
            icon: 'MessageSquareIcon',
            pageName: 'Chat',
            title: 'Chat',
        });
    }

    menu.push({
        icon: 'LaptopIcon',
        pageName: 'listaInstancias',
        title: 'Instâncias',
    });

    // CRM agrupando Clientes e Contatos
    if (findChatPedidos) {
        menu.push({
            icon: 'MessageSquareTextIcon',
            title: 'Mensagens',
            subMenu: [
                {
                    icon: 'MessageSquarePlusIcon',
                    pageName: 'listaMensagem',
                    title: 'Cadastro de Mensagens',
                    desc: 'Configure as mensagens automáticas.',
                },
                {
                    icon: 'ChartAreaIcon',
                    pageName: 'dashGeral',
                    title: 'Dashboard Geral',
                    desc: 'Visualize o desempenho e as métricas da sua operação.',
                },
            ],
        });
    }

    // CRM agrupando Clientes e Contatos
    if (findCRM) {
        menu.push({
            icon: 'UsersRoundIcon',
            title: 'CRM',
            subMenu: [
                //Habilitar quando ajustar os perfis
                {
                    icon: 'MessageSquarePlusIcon',
                    pageName: 'listaMensagem',
                    title: 'Cadastro de Mensagens',
                    desc: 'Configure as mensagens automáticas.',
                },
                // {
                //     icon: 'ChartAreaIcon',
                //     pageName: 'dashGeral',
                //     title: 'Dashboard Geral',
                //     desc: 'Visualize o desempenho e as métricas da sua operação.',
                // },
                {
                    icon: 'UsersRoundIcon',
                    pageName: 'listaClientes',
                    title: 'Clientes',
                    desc: 'Gerencie seus clientes cadastrados e suas informações de contato.',
                },
                {
                    icon: 'BookUserIcon',
                    pageName: 'listaContatos',
                    title: 'Contatos',
                    desc: 'Gerencie os contatos dos clientes cadastrados.',
                },
                {
                    icon: 'ListPlusIcon',
                    pageName: 'cadMotivosAtendimento',
                    title: 'Assunto de Atendimento',
                    desc: 'Gerencie os assuntos de atendimento.',
                },
                {
                    icon: 'MegaphoneIcon',
                    pageName: 'listaCampanhas',
                    title: 'Campanhas',
                    desc: 'Gerencie as campanhas de marketing.',
                },
            ],
        });
    }

    // API agrupando Dashboard
    if (findApi) {
        menu.push({
            icon: 'WaypointsIcon',
            title: 'API',
            subMenu: [
                {
                    icon: 'LayoutDashboardIcon',
                    pageName: 'dashboard',
                    title: 'Dashboard',
                    desc: 'Visualize o desempenho e as métricas da API.',
                },
                {
                    icon: 'BookTextIcon',
                    pageName: 'apiDocs',
                    title: 'Documentação',
                    desc: 'Acesse a documentação da API para entender como utilizá-la.',
                },
            ],
        });
    }

    // if (findChatPedidos || findCRM) {
    //     if (dados.tp_privilegio != 'U') {
    //         menu.push({
    //             icon: 'MessageSquarePlusIcon',
    //             pageName: 'listaMensagem',
    //             title: 'Mensagem',
    //         });
    //     }

    //     menu.push({
    //         icon: 'ChartAreaIcon',
    //         pageName: 'dashGeral',
    //         title: 'Gráfico de Desempenhos',
    //     });
    // }

    if (dados.tp_privilegio == 'O') {
        if (findChatPedidos || findCRM) {
            menu.push({
                icon: 'MessageSquareTextIcon',
                pageName: 'listaMensagemPadrao',
                title: 'Mensagem Padrão',
            });
        }
    }

    // if (findApi) {
    //     menu.push({
    //         icon: 'WaypointsIcon',
    //         pageName: 'apiDocs',
    //         title: 'API',
    //     });
    // }

    if (dados.in_usuarioadmin) {
        menu.push({
            icon: 'HandshakeIcon',
            pageName: 'listaCaixa',
            title: 'Caixa',
        });
    }

    // ===== NOVO SISTEMA DE CONTROLE DE ACESSO =====
    // Verificar se o usuário tem perfis configurados no novo sistema
    try {
        if (dados.cd_usuario) {
            const menuAcesso = await AccessControlService.obterMenuPorUsuario(dados.cd_usuario);

            if (menuAcesso.menu && menuAcesso.menu.length > 0) {
                // Adicionar itens do sistema de controle de acesso ao menu
                menu.push(...menuAcesso.menu);

                // Salvar informações de permissões no localStorage para uso posterior
                localStorage.setItem('telasPermitidas', JSON.stringify(menuAcesso.telasPermitidas || []));
            }
        }
    } catch (error) {
        console.warn('Erro ao carregar menu do sistema de controle de acesso:', error);
        // Não interrompe o carregamento do menu principal
    }

    return { menu };
}
