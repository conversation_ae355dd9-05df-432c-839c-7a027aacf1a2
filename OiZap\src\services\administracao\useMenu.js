//import UsuariosServices from '@/services/administracao/UsuariosServices';
import AccessControlService from '@/services/administracao/AccessControlService';

import { sub } from 'date-fns';

export async function obtemMenu(dados) {
    console.log('🚀 [DEBUG] useMenu.obtemMenu - Iniciando');
    console.log('🚀 [DEBUG] useMenu.obtemMenu - dados:', dados);
    console.log('🚀 [DEBUG] useMenu.obtemMenu - cd_usuario:', dados.cd_usuario);

    var menu = [];

    // ===== VERIFICAR PERMISSÕES DO USUÁRIO =====
    let telasPermitidas = [];
    let isAdminUser = false;

    try {
        if (dados.cd_usuario) {
            const menuAcesso = await AccessControlService.obterMenuPorUsuario(dados.cd_usuario);
            console.log('🔐 [DEBUG] useMenu - Permissões do usuário:', menuAcesso);

            if (menuAcesso.telasPermitidas && menuAcesso.telasPermitidas.length > 0) {
                telasPermitidas = menuAcesso.telasPermitidas.map(item => item.tela?.ds_rota).filter(Boolean);
                console.log('🎯 [DEBUG] useMenu - Rotas permitidas:', telasPermitidas);

                // Verificar se é administrador
                const telasAdmin = [
                    '/oizap/gestao-perfis',
                    '/oizap/gestao-telas',
                    '/oizap/gestao-perfis-telas',
                    '/oizap/gestao-usuarios-perfis'
                ];
                isAdminUser = telasAdmin.some(rota => telasPermitidas.includes(rota));
                console.log('👑 [DEBUG] useMenu - É administrador?', isAdminUser);
            }
        }
    } catch (error) {
        console.warn('❌ useMenu: Erro ao verificar permissões:', error);
    }

    // Função para verificar se o usuário tem acesso a uma rota
    const temAcesso = (rota) => {
        if (!rota) return true; // Se não tem rota específica, libera
        if (telasPermitidas.length === 0) return true; // Se não tem controle de acesso, libera tudo
        return telasPermitidas.includes(rota);
    };

    const findApi = dados.modulos.find((modulo) => modulo.nm_modulo == 'API');
    const findChatPedidos = dados.modulos.find((modulo) => modulo.nm_modulo == 'ChatBot/Pedidos');
    const findCRM = dados.modulos.find((modulo) => modulo.nm_modulo == 'CRM');

    // Chat - verificar permissão
    if ((findChatPedidos || findCRM) && temAcesso('/oizap/chat')) {
        menu.push({
            icon: 'MessageSquareIcon',
            pageName: 'Chat',
            title: 'Chat',
        });
    }

    // Instâncias - verificar permissão
    if (temAcesso('/oizap/listaInstancias')) {
        menu.push({
            icon: 'LaptopIcon',
            pageName: 'listaInstancias',
            title: 'Instâncias',
        });
    }

    // Mensagens - verificar permissão
    if (findChatPedidos && temAcesso('/oizap/mensagens')) {
        const subMenuMensagens = [];

        // Verificar cada submenu individualmente
        if (temAcesso('/oizap/listaMensagem')) {
            subMenuMensagens.push({
                icon: 'MessageSquarePlusIcon',
                pageName: 'listaMensagem',
                title: 'Cadastro de Mensagens',
                desc: 'Configure as mensagens automáticas.',
            });
        }

        if (temAcesso('/oizap/dashGeral')) {
            subMenuMensagens.push({
                icon: 'ChartAreaIcon',
                pageName: 'dashGeral',
                title: 'Dashboard Geral',
                desc: 'Visualize o desempenho e as métricas da sua operação.',
            });
        }

        // Só adiciona o menu se tiver pelo menos um submenu
        if (subMenuMensagens.length > 0) {
            menu.push({
                icon: 'MessageSquareTextIcon',
                title: 'Mensagens',
                subMenu: subMenuMensagens,
            });
        }
    }

    // CRM - verificar permissão
    if (findCRM && temAcesso('/oizap/crm')) {
        const subMenuCRM = [];

        // Verificar cada submenu individualmente
        if (temAcesso('/oizap/listaMensagem')) {
            subMenuCRM.push({
                icon: 'MessageSquarePlusIcon',
                pageName: 'listaMensagem',
                title: 'Cadastro de Mensagens',
                desc: 'Configure as mensagens automáticas.',
            });
        }

        if (temAcesso('/oizap/listaClientes')) {
            subMenuCRM.push({
                icon: 'UsersRoundIcon',
                pageName: 'listaClientes',
                title: 'Clientes',
                desc: 'Gerencie seus clientes cadastrados e suas informações de contato.',
            });
        }

        if (temAcesso('/oizap/listaContatos')) {
            subMenuCRM.push({
                icon: 'BookUserIcon',
                pageName: 'listaContatos',
                title: 'Contatos',
                desc: 'Gerencie os contatos dos clientes cadastrados.',
            });
        }

        if (temAcesso('/oizap/cadMotivosAtendimento')) {
            subMenuCRM.push({
                icon: 'ListPlusIcon',
                pageName: 'cadMotivosAtendimento',
                title: 'Assunto de Atendimento',
                desc: 'Gerencie os assuntos de atendimento.',
            });
        }

        if (temAcesso('/oizap/listaCampanhas')) {
            subMenuCRM.push({
                icon: 'MegaphoneIcon',
                pageName: 'listaCampanhas',
                title: 'Campanhas',
                desc: 'Gerencie as campanhas de marketing.',
            });
        }

        // Só adiciona o menu se tiver pelo menos um submenu
        if (subMenuCRM.length > 0) {
            menu.push({
                icon: 'UsersRoundIcon',
                title: 'CRM',
                subMenu: subMenuCRM,
            });
        }
    }

    // API - verificar permissão
    if (findApi && temAcesso('/oizap/api')) {
        const subMenuAPI = [];

        // Verificar cada submenu individualmente
        if (temAcesso('/oizap/dashboard')) {
            subMenuAPI.push({
                icon: 'LayoutDashboardIcon',
                pageName: 'dashboard',
                title: 'Dashboard',
                desc: 'Visualize o desempenho e as métricas da API.',
            });
        }

        if (temAcesso('/oizap/apiDocs')) {
            subMenuAPI.push({
                icon: 'BookTextIcon',
                pageName: 'apiDocs',
                title: 'Documentação',
                desc: 'Acesse a documentação da API para entender como utilizá-la.',
            });
        }

        // Só adiciona o menu se tiver pelo menos um submenu
        if (subMenuAPI.length > 0) {
            menu.push({
                icon: 'WaypointsIcon',
                title: 'API',
                subMenu: subMenuAPI,
            });
        }
    }

    // if (findChatPedidos || findCRM) {
    //     if (dados.tp_privilegio != 'U') {
    //         menu.push({
    //             icon: 'MessageSquarePlusIcon',
    //             pageName: 'listaMensagem',
    //             title: 'Mensagem',
    //         });
    //     }

    //     menu.push({
    //         icon: 'ChartAreaIcon',
    //         pageName: 'dashGeral',
    //         title: 'Gráfico de Desempenhos',
    //     });
    // }

    // Mensagem Padrão - verificar permissão
    if (dados.tp_privilegio == 'O' && (findChatPedidos || findCRM) && temAcesso('/oizap/listaMensagemPadrao')) {
        menu.push({
            icon: 'MessageSquareTextIcon',
            pageName: 'listaMensagemPadrao',
            title: 'Mensagem Padrão',
        });
    }

    // Caixa - verificar permissão
    if (dados.in_usuarioadmin && temAcesso('/oizap/listaCaixa')) {
        menu.push({
            icon: 'HandshakeIcon',
            pageName: 'listaCaixa',
            title: 'Caixa',
        });
    }

    // ===== MENU ADMINISTRATIVO =====
    // Adicionar menu administrativo se o usuário for administrador
    if (isAdminUser) {
        console.log('🔧 useMenu: Adicionando menu administrativo');
        const subMenuAdmin = [];

        // Verificar cada submenu administrativo individualmente
        if (temAcesso('/oizap/gestao-perfis')) {
            subMenuAdmin.push({
                icon: 'UsersIcon',
                pageName: 'gestao-perfis',
                title: 'Gestão de Perfis',
                desc: 'Gerencie os perfis de acesso do sistema.',
            });
        }

        if (temAcesso('/oizap/gestao-telas')) {
            subMenuAdmin.push({
                icon: 'LayoutIcon',
                pageName: 'gestao-telas',
                title: 'Gestão de Telas',
                desc: 'Configure as telas disponíveis no sistema.',
            });
        }

        if (temAcesso('/oizap/gestao-perfis-telas')) {
            subMenuAdmin.push({
                icon: 'LinkIcon',
                pageName: 'gestao-perfis-telas',
                title: 'Perfis x Telas',
                desc: 'Associe perfis às telas e configure permissões.',
            });
        }

        if (temAcesso('/oizap/gestao-usuarios-perfis')) {
            subMenuAdmin.push({
                icon: 'UserCheckIcon',
                pageName: 'gestao-usuarios-perfis',
                title: 'Usuários x Perfis',
                desc: 'Associe usuários aos perfis de acesso.',
            });
        }

        // Só adiciona o menu se tiver pelo menos um submenu
        if (subMenuAdmin.length > 0) {
            menu.push({
                icon: 'SettingsIcon',
                title: 'Administração',
                subMenu: subMenuAdmin,
            });
        }

        // Salvar informações de permissões no localStorage para uso posterior
        localStorage.setItem('telasPermitidas', JSON.stringify(telasPermitidas || []));
    } else {
        console.log('❌ useMenu: Usuário não tem acesso às telas administrativas');
    }

    console.log('🚀 [DEBUG] useMenu.obtemMenu - Menu final gerado:');
    console.log('🚀 [DEBUG] useMenu.obtemMenu - Total de itens no menu:', menu.length);
    console.log('🚀 [DEBUG] useMenu.obtemMenu - Menu completo:', JSON.stringify(menu, null, 2));

    // Verificar se há item de Administração
    const itemAdmin = menu.find(item => item.title === 'Administração');
    if (itemAdmin) {
        console.log('✅ [DEBUG] useMenu.obtemMenu - Item Administração encontrado:', itemAdmin);
    } else {
        console.log('❌ [DEBUG] useMenu.obtemMenu - Item Administração NÃO encontrado');
    }

    // Verificar localStorage
    const telasPermitidas_localStorage = localStorage.getItem('telasPermitidas');
    console.log('🔍 [DEBUG] useMenu.obtemMenu - telasPermitidas no localStorage:', telasPermitidas_localStorage);

    return { menu };
}
