//import UsuariosServices from '@/services/administracao/UsuariosServices';
import AccessControlService from '@/services/administracao/AccessControlService';

import { sub } from 'date-fns';

export async function obtemMenu(dados) {
    console.log('🚀 [DEBUG] useMenu.obtemMenu - Iniciando');
    console.log('🚀 [DEBUG] useMenu.obtemMenu - dados:', dados);
    console.log('🚀 [DEBUG] useMenu.obtemMenu - cd_usuario:', dados.cd_usuario);

    var menu = [];

    const findApi = dados.modulos.find((modulo) => modulo.nm_modulo == 'API');
    const findChatPedidos = dados.modulos.find((modulo) => modulo.nm_modulo == 'ChatBot/Pedidos');
    const findCRM = dados.modulos.find((modulo) => modulo.nm_modulo == 'CRM');

    if (findChatPedidos || findCRM) {
        menu.push({
            icon: 'MessageSquareIcon',
            pageName: 'Chat',
            title: 'Chat',
        });
    }

    menu.push({
        icon: 'LaptopIcon',
        pageName: 'listaInstancias',
        title: 'Instâncias',
    });

    // CRM agrupando Clientes e Contatos
    if (findChatPedidos) {
        menu.push({
            icon: 'MessageSquareTextIcon',
            title: 'Mensagens',
            subMenu: [
                {
                    icon: 'MessageSquarePlusIcon',
                    pageName: 'listaMensagem',
                    title: 'Cadastro de Mensagens',
                    desc: 'Configure as mensagens automáticas.',
                },
                {
                    icon: 'ChartAreaIcon',
                    pageName: 'dashGeral',
                    title: 'Dashboard Geral',
                    desc: 'Visualize o desempenho e as métricas da sua operação.',
                },
            ],
        });
    }

    // CRM agrupando Clientes e Contatos
    if (findCRM) {
        menu.push({
            icon: 'UsersRoundIcon',
            title: 'CRM',
            subMenu: [
                //Habilitar quando ajustar os perfis
                {
                    icon: 'MessageSquarePlusIcon',
                    pageName: 'listaMensagem',
                    title: 'Cadastro de Mensagens',
                    desc: 'Configure as mensagens automáticas.',
                },
                // {
                //     icon: 'ChartAreaIcon',
                //     pageName: 'dashGeral',
                //     title: 'Dashboard Geral',
                //     desc: 'Visualize o desempenho e as métricas da sua operação.',
                // },
                {
                    icon: 'UsersRoundIcon',
                    pageName: 'listaClientes',
                    title: 'Clientes',
                    desc: 'Gerencie seus clientes cadastrados e suas informações de contato.',
                },
                {
                    icon: 'BookUserIcon',
                    pageName: 'listaContatos',
                    title: 'Contatos',
                    desc: 'Gerencie os contatos dos clientes cadastrados.',
                },
                {
                    icon: 'ListPlusIcon',
                    pageName: 'cadMotivosAtendimento',
                    title: 'Assunto de Atendimento',
                    desc: 'Gerencie os assuntos de atendimento.',
                },
                {
                    icon: 'MegaphoneIcon',
                    pageName: 'listaCampanhas',
                    title: 'Campanhas',
                    desc: 'Gerencie as campanhas de marketing.',
                },
            ],
        });
    }

    // API agrupando Dashboard
    if (findApi) {
        menu.push({
            icon: 'WaypointsIcon',
            title: 'API',
            subMenu: [
                {
                    icon: 'LayoutDashboardIcon',
                    pageName: 'dashboard',
                    title: 'Dashboard',
                    desc: 'Visualize o desempenho e as métricas da API.',
                },
                {
                    icon: 'BookTextIcon',
                    pageName: 'apiDocs',
                    title: 'Documentação',
                    desc: 'Acesse a documentação da API para entender como utilizá-la.',
                },
            ],
        });
    }

    // if (findChatPedidos || findCRM) {
    //     if (dados.tp_privilegio != 'U') {
    //         menu.push({
    //             icon: 'MessageSquarePlusIcon',
    //             pageName: 'listaMensagem',
    //             title: 'Mensagem',
    //         });
    //     }

    //     menu.push({
    //         icon: 'ChartAreaIcon',
    //         pageName: 'dashGeral',
    //         title: 'Gráfico de Desempenhos',
    //     });
    // }

    if (dados.tp_privilegio == 'O') {
        if (findChatPedidos || findCRM) {
            menu.push({
                icon: 'MessageSquareTextIcon',
                pageName: 'listaMensagemPadrao',
                title: 'Mensagem Padrão',
            });
        }
    }

    // if (findApi) {
    //     menu.push({
    //         icon: 'WaypointsIcon',
    //         pageName: 'apiDocs',
    //         title: 'API',
    //     });
    // }

    if (dados.in_usuarioadmin) {
        menu.push({
            icon: 'HandshakeIcon',
            pageName: 'listaCaixa',
            title: 'Caixa',
        });
    }

    // ===== NOVO SISTEMA DE CONTROLE DE ACESSO =====
    // Verificar se o usuário tem perfis configurados no novo sistema
    try {
        console.log('🔍 useMenu: Verificando controle de acesso para usuário:', dados.cd_usuario);

        if (dados.cd_usuario) {
            const menuAcesso = await AccessControlService.obterMenuPorUsuario(dados.cd_usuario);
            console.log('📋 useMenu: Menu de acesso retornado:', menuAcesso);

            if (menuAcesso.menu && menuAcesso.menu.length > 0) {
                console.log('✅ useMenu: Adicionando itens do controle de acesso ao menu');
                // Adicionar itens do sistema de controle de acesso ao menu
                menu.push(...menuAcesso.menu);

                // Salvar informações de permissões no localStorage para uso posterior
                localStorage.setItem('telasPermitidas', JSON.stringify(menuAcesso.telasPermitidas || []));
            }

            // Verificar se o usuário tem acesso às telas administrativas
            const telasAdmin = [
                '/oizap/gestao-perfis',
                '/oizap/gestao-telas',
                '/oizap/gestao-perfis-telas',
                '/oizap/gestao-usuarios-perfis'
            ];

            const temAcessoAdmin = menuAcesso.telasPermitidas?.some(item =>
                telasAdmin.includes(item.tela?.ds_rota)
            );

            console.log('🔐 useMenu: Tem acesso admin?', temAcessoAdmin);
            console.log('🎯 useMenu: Telas permitidas:', menuAcesso.telasPermitidas?.map(t => t.tela?.ds_rota));

            if (temAcessoAdmin) {
                console.log('🔧 useMenu: Adicionando menu administrativo');
                // Adicionar menu administrativo
                menu.push({
                    icon: 'SettingsIcon',
                    title: 'Administração',
                    subMenu: [
                        {
                            icon: 'UsersIcon',
                            pageName: 'gestao-perfis',
                            title: 'Gestão de Perfis',
                            desc: 'Gerencie os perfis de acesso do sistema.',
                        },
                        {
                            icon: 'LayoutIcon',
                            pageName: 'gestao-telas',
                            title: 'Gestão de Telas',
                            desc: 'Configure as telas disponíveis no sistema.',
                        },
                        {
                            icon: 'LinkIcon',
                            pageName: 'gestao-perfis-telas',
                            title: 'Perfis x Telas',
                            desc: 'Associe perfis às telas e configure permissões.',
                        },
                        {
                            icon: 'UserCheckIcon',
                            pageName: 'gestao-usuarios-perfis',
                            title: 'Usuários x Perfis',
                            desc: 'Associe usuários aos perfis de acesso.',
                        }
                    ],
                });
            } else {
                console.log('❌ useMenu: Usuário não tem acesso às telas administrativas');
            }
        }
    } catch (error) {
        console.warn('❌ useMenu: Erro ao carregar menu do sistema de controle de acesso:', error);
        // Não interrompe o carregamento do menu principal
    }

    console.log('🚀 [DEBUG] useMenu.obtemMenu - Menu final gerado:');
    console.log('🚀 [DEBUG] useMenu.obtemMenu - Total de itens no menu:', menu.length);
    console.log('🚀 [DEBUG] useMenu.obtemMenu - Menu completo:', JSON.stringify(menu, null, 2));

    // Verificar se há item de Administração
    const itemAdmin = menu.find(item => item.title === 'Administração');
    if (itemAdmin) {
        console.log('✅ [DEBUG] useMenu.obtemMenu - Item Administração encontrado:', itemAdmin);
    } else {
        console.log('❌ [DEBUG] useMenu.obtemMenu - Item Administração NÃO encontrado');
    }

    // Verificar localStorage
    const telasPermitidas = localStorage.getItem('telasPermitidas');
    console.log('🔍 [DEBUG] useMenu.obtemMenu - telasPermitidas no localStorage:', telasPermitidas);

    return { menu };
}
