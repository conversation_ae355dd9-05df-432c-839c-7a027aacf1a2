<template>
    <div class="intro-y flex flex-col sm:flex-row items-center mt-8">
        <h2 class="text-lg font-medium mr-auto">Gestão de Perfis x Telas</h2>
        <div class="w-full sm:w-auto flex mt-4 sm:mt-0">
            <select v-model="perfilSelecionado" class="form-select w-auto mr-2" @change="carregarAssociacoes">
                <option value="">Selecione um perfil</option>
                <option v-for="perfil in perfis" :key="perfil.cd_perfil" :value="perfil.cd_perfil">
                    {{ perfil.nm_perfil }}
                </option>
            </select>
            <button 
                @click="abrirModalAssociar" 
                class="btn btn-primary shadow-md"
                :disabled="!perfilSelecionado"
            >
                <PlusIcon class="w-4 h-4 mr-2" />
                Associar Tela
            </button>
        </div>
    </div>

    <!-- Informações do Perfil -->
    <div v-if="perfilAtual" class="intro-y box p-5 mt-5">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mr-4">
                <UsersIcon class="w-6 h-6 text-primary" />
            </div>
            <div>
                <h3 class="text-lg font-medium">{{ perfilAtual.nm_perfil }}</h3>
                <p class="text-slate-500">{{ associacoes.length }} tela(s) associada(s)</p>
            </div>
        </div>
    </div>

    <!-- Lista de Associações -->
    <div v-if="perfilSelecionado" class="intro-y grid grid-cols-12 gap-6 mt-5">
        <div class="col-span-12">
            <div class="intro-y box">
                <div class="flex flex-col sm:flex-row items-center p-5 border-b border-slate-200/60">
                    <h2 class="font-medium text-base mr-auto">Telas Associadas</h2>
                    <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                        <input
                            v-model="filtro"
                            type="text"
                            class="form-control w-full sm:w-64"
                            placeholder="Buscar tela..."
                        />
                    </div>
                </div>
                <div class="p-5">
                    <div v-if="associacoes.length === 0" class="text-center py-8">
                        <LayoutIcon class="w-16 h-16 mx-auto text-slate-300 mb-4" />
                        <p class="text-slate-500">Nenhuma tela associada a este perfil</p>
                        <button @click="abrirModalAssociar" class="btn btn-primary mt-4">
                            Associar primeira tela
                        </button>
                    </div>
                    <div v-else class="overflow-x-auto">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th class="whitespace-nowrap">Tela</th>
                                    <th class="whitespace-nowrap">Rota</th>
                                    <th class="whitespace-nowrap text-center">Visualizar</th>
                                    <th class="whitespace-nowrap text-center">Inserir</th>
                                    <th class="whitespace-nowrap text-center">Alterar</th>
                                    <th class="whitespace-nowrap text-center">Excluir</th>
                                    <th class="whitespace-nowrap text-center">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="associacao in associacoesFiltradas" :key="associacao.cd_perfil_tela">
                                    <td class="font-medium">{{ associacao.tela?.nm_tela }}</td>
                                    <td>
                                        <code class="text-xs bg-slate-100 px-2 py-1 rounded">
                                            {{ associacao.tela?.ds_rota }}
                                        </code>
                                    </td>
                                    <td class="text-center">
                                        <input
                                            type="checkbox"
                                            :checked="associacao.in_visualizar"
                                            @change="atualizarPermissao(associacao, 'in_visualizar', $event.target.checked)"
                                            class="form-check-input"
                                        />
                                    </td>
                                    <td class="text-center">
                                        <input
                                            type="checkbox"
                                            :checked="associacao.in_inserir"
                                            @change="atualizarPermissao(associacao, 'in_inserir', $event.target.checked)"
                                            class="form-check-input"
                                        />
                                    </td>
                                    <td class="text-center">
                                        <input
                                            type="checkbox"
                                            :checked="associacao.in_alterar"
                                            @change="atualizarPermissao(associacao, 'in_alterar', $event.target.checked)"
                                            class="form-check-input"
                                        />
                                    </td>
                                    <td class="text-center">
                                        <input
                                            type="checkbox"
                                            :checked="associacao.in_excluir"
                                            @change="atualizarPermissao(associacao, 'in_excluir', $event.target.checked)"
                                            class="form-check-input"
                                        />
                                    </td>
                                    <td class="text-center">
                                        <button
                                            @click="desassociarTela(associacao)"
                                            class="btn btn-outline-danger w-8 h-8"
                                            title="Remover associação"
                                        >
                                            <TrashIcon class="w-4 h-4" />
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Estado inicial -->
    <div v-else class="intro-y box p-8 mt-5 text-center">
        <UsersIcon class="w-16 h-16 mx-auto text-slate-300 mb-4" />
        <h3 class="text-lg font-medium mb-2">Selecione um perfil</h3>
        <p class="text-slate-500">Escolha um perfil acima para gerenciar suas associações com telas</p>
    </div>

    <!-- Modal Associar Tela -->
    <Modal :show="modalAssociar" @hidden="fecharModalAssociar">
        <ModalHeader>
            <h2 class="font-medium text-base mr-auto">Associar Tela ao Perfil</h2>
        </ModalHeader>
        <ModalBody>
            <div class="grid grid-cols-12 gap-4 gap-y-3">
                <div class="col-span-12">
                    <label class="form-label">Perfil</label>
                    <input
                        :value="perfilAtual?.nm_perfil"
                        type="text"
                        class="form-control"
                        readonly
                    />
                </div>
                <div class="col-span-12">
                    <label class="form-label">Tela *</label>
                    <select
                        v-model="formAssociacao.cd_tela"
                        class="form-select"
                        :class="{ 'border-danger': erros.cd_tela }"
                    >
                        <option value="">Selecione uma tela</option>
                        <option 
                            v-for="tela in telasDisponiveis" 
                            :key="tela.cd_tela" 
                            :value="tela.cd_tela"
                        >
                            {{ tela.nm_tela }} ({{ tela.ds_rota }})
                        </option>
                    </select>
                    <div v-if="erros.cd_tela" class="text-danger text-xs mt-1">
                        {{ erros.cd_tela }}
                    </div>
                </div>
                <div class="col-span-12">
                    <label class="form-label">Permissões</label>
                    <div class="grid grid-cols-2 gap-4 mt-2">
                        <div class="form-check">
                            <input
                                v-model="formAssociacao.in_visualizar"
                                type="checkbox"
                                class="form-check-input"
                                id="perm-visualizar"
                            />
                            <label class="form-check-label" for="perm-visualizar">
                                Visualizar
                            </label>
                        </div>
                        <div class="form-check">
                            <input
                                v-model="formAssociacao.in_inserir"
                                type="checkbox"
                                class="form-check-input"
                                id="perm-inserir"
                            />
                            <label class="form-check-label" for="perm-inserir">
                                Inserir
                            </label>
                        </div>
                        <div class="form-check">
                            <input
                                v-model="formAssociacao.in_alterar"
                                type="checkbox"
                                class="form-check-input"
                                id="perm-alterar"
                            />
                            <label class="form-check-label" for="perm-alterar">
                                Alterar
                            </label>
                        </div>
                        <div class="form-check">
                            <input
                                v-model="formAssociacao.in_excluir"
                                type="checkbox"
                                class="form-check-input"
                                id="perm-excluir"
                            />
                            <label class="form-check-label" for="perm-excluir">
                                Excluir
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </ModalBody>
        <ModalFooter>
            <button @click="fecharModalAssociar" class="btn btn-outline-secondary w-20 mr-1">
                Cancelar
            </button>
            <button @click="salvarAssociacao" class="btn btn-primary w-20" :disabled="salvando">
                <LoadingIcon v-if="salvando" class="w-4 h-4 mr-2" />
                Associar
            </button>
        </ModalFooter>
    </Modal>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import AccessControlService from '@/services/administracao/AccessControlService';

// Estados
const perfis = ref([]);
const telas = ref([]);
const associacoes = ref([]);
const perfilSelecionado = ref('');
const filtro = ref('');
const modalAssociar = ref(false);
const salvando = ref(false);

// Formulário
const formAssociacao = ref({
    cd_tela: '',
    in_visualizar: true,
    in_inserir: false,
    in_alterar: false,
    in_excluir: false
});

const erros = ref({});

// Computed
const perfilAtual = computed(() => {
    return perfis.value.find(p => p.cd_perfil == perfilSelecionado.value);
});

const associacoesFiltradas = computed(() => {
    if (!filtro.value) return associacoes.value;
    
    const termo = filtro.value.toLowerCase();
    return associacoes.value.filter(assoc =>
        assoc.tela?.nm_tela.toLowerCase().includes(termo) ||
        assoc.tela?.ds_rota.toLowerCase().includes(termo)
    );
});

const telasDisponiveis = computed(() => {
    const telasAssociadas = associacoes.value.map(a => a.cd_tela);
    return telas.value.filter(tela => !telasAssociadas.includes(tela.cd_tela));
});

// Métodos
async function carregarDados() {
    try {
        const [responsePerfis, responseTelas] = await Promise.all([
            AccessControlService.listarPerfis(),
            AccessControlService.listarTelas()
        ]);
        
        if (responsePerfis.statuscode === 200) {
            perfis.value = responsePerfis.data;
        }
        
        if (responseTelas.statuscode === 200) {
            telas.value = responseTelas.data;
        }
    } catch (error) {
        console.error('Erro ao carregar dados:', error);
    }
}

async function carregarAssociacoes() {
    if (!perfilSelecionado.value) {
        associacoes.value = [];
        return;
    }

    try {
        const response = await AccessControlService.listarAssociacoesPerfisTelasPorPerfil(perfilSelecionado.value);
        
        if (response.statuscode === 200) {
            associacoes.value = response.data;
        }
    } catch (error) {
        console.error('Erro ao carregar associações:', error);
    }
}

function abrirModalAssociar() {
    formAssociacao.value = {
        cd_tela: '',
        in_visualizar: true,
        in_inserir: false,
        in_alterar: false,
        in_excluir: false
    };
    erros.value = {};
    modalAssociar.value = true;
}

function fecharModalAssociar() {
    modalAssociar.value = false;
    formAssociacao.value = {
        cd_tela: '',
        in_visualizar: true,
        in_inserir: false,
        in_alterar: false,
        in_excluir: false
    };
    erros.value = {};
}

async function salvarAssociacao() {
    try {
        salvando.value = true;
        erros.value = {};

        if (!formAssociacao.value.cd_tela) {
            erros.value.cd_tela = 'Selecione uma tela';
            return;
        }

        const dados = {
            cd_perfil: perfilSelecionado.value,
            cd_tela: formAssociacao.value.cd_tela,
            in_visualizar: formAssociacao.value.in_visualizar,
            in_inserir: formAssociacao.value.in_inserir,
            in_alterar: formAssociacao.value.in_alterar,
            in_excluir: formAssociacao.value.in_excluir
        };

        const response = await AccessControlService.associarPerfilTela(dados);

        if (response.statuscode === 200) {
            await carregarAssociacoes();
            fecharModalAssociar();
        } else {
            console.error('Erro ao salvar associação:', response.message);
        }
    } catch (error) {
        console.error('Erro ao salvar associação:', error);
    } finally {
        salvando.value = false;
    }
}

async function atualizarPermissao(associacao, campo, valor) {
    try {
        const dados = {
            cd_perfil_tela: associacao.cd_perfil_tela,
            [campo]: valor
        };

        const response = await AccessControlService.atualizarPermissoes(dados);

        if (response.statuscode === 200) {
            // Atualizar localmente
            associacao[campo] = valor;
        } else {
            console.error('Erro ao atualizar permissão:', response.message);
            // Reverter mudança
            associacao[campo] = !valor;
        }
    } catch (error) {
        console.error('Erro ao atualizar permissão:', error);
        // Reverter mudança
        associacao[campo] = !valor;
    }
}

async function desassociarTela(associacao) {
    if (!confirm(`Tem certeza que deseja remover a associação com "${associacao.tela?.nm_tela}"?`)) {
        return;
    }

    try {
        const response = await AccessControlService.desassociarPerfilTela(
            associacao.cd_perfil,
            associacao.cd_tela
        );

        if (response.statuscode === 200) {
            await carregarAssociacoes();
        } else {
            console.error('Erro ao desassociar tela:', response.message);
        }
    } catch (error) {
        console.error('Erro ao desassociar tela:', error);
    }
}

// Lifecycle
onMounted(() => {
    carregarDados();
});
</script>
