import { createRouter, createWebHistory } from 'vue-router';
import SimpleMenu from '../layouts/simple-menu/Main.vue';
import SimpleMenu2 from '../layouts/simple-menu/Main2.vue';

import hosts from '@/utils/hosts';

import Login from '../views/login/Main.vue';
import loginExterno from '../views/login/loginExterno.vue';
import Home from '../views/home/<USER>';
//import Site from '../views/site/Site.vue';
//import teste from '../views/page-1/teste.vue';

import listaInstancias from '../views/administracao/instancia/listaInstancias.vue';
//import teste from '../views/administracao/instancia/teste.vue';
import cadInstancia from '../views/administracao/instancia/cadInstancia.vue';
import viewInstancia from '../views/administracao/instancia/viewInstancia.vue';

import listaCaixa from '../views/administracao/estabelecimento/listaCaixa.vue';
import dashGeral from '../views/dashboard/dashGeral.vue';

import listaMensagem from '../views/mensagem/listaMensagem.vue';
import listaMensagemPadrao from '../views/mensagem/listaMensagem.vue';
import relMensagens from '../views/mensagem/relMensagens.vue';

import listaClientes from '../views/clientes/listaClientes.vue';
import listaContatos from '../views/contatos/listaContatos.vue';

import Chat from '../views/chat/Chat.vue';
import Chat_OLD from '../views/chat/Chat_OLD.vue';
//import Instancia from '../views/chat/Instancia.vue';
import FluxoAtendimento from '../views/configuracao/FluxoAtendimento.vue';
import listaFluxos from '../views/configuracao/listaFluxos.vue';
import Integracao from '../views/configuracao/Integracao.vue';
import Atendimento from '../views/kanban/Atendimento.vue';

import routerAdministracao from '@/router/administracao';
import routerCRM from '@/router/crm';
import apiDocs from '../views/api/apiDocs.vue';
import Dashboard from '../views/administracao/instancia/Dashboard.vue';

import cadMotivosAtendimento from '../views/chat/motivos-atendimento/cadMotivosAtendimento.vue';

const routes = [
    /* {
    path: `${hosts.app}`,
    name: 'Site',
    component: Site,
  },*/
    /*  {
    path: `${hosts.app}/login`,
    name: 'Login',
    component: Login,
  },*/
    // {
    //     path: `${hosts.app}/teste`,
    //     name: 'teste',
    //     component: teste,
    // },
    {
        path: `${hosts.app}`,
        name: 'Login',
        component: Login,
    },
    /* {
    path: `${hosts.app}/cadastrar`,
    name: 'Cadastrar',
    component: Cadastrar,
  },*/
    {
        path: `${hosts.app}/auth/integracao/:iduser`,
        name: 'loginExterno',
        component: loginExterno,
        props: true, // Permite passar o parâmetro como propriedade
    },
    {
        // component: SimpleMenu,
        component: SimpleMenu2,
        children: [
            {
                path: `${hosts.app}/home`,
                name: 'Home',
                component: Home,
            },
            {
                path: `${hosts.app}` + '/listaInstancias',
                name: 'listaInstancias',
                component: listaInstancias,
            },
            // {
            //   path: `${hosts.app}` + '/teste',
            //   name: 'teste',
            //   component: teste,
            // },
            {
                path: `${hosts.app}` + '/cadInstancia',
                name: 'cadInstancia',
                component: cadInstancia,
            },
            {
                path: `${hosts.app}` + '/viewInstancia/:idInstancia?',
                name: 'viewInstancia',
                component: viewInstancia,
            },
            /* {
        path: `${hosts.app}/instancia`,
        name: 'Instancia',
        component: Instancia,
      },*/
            {
                path: `${hosts.app}/atendimento`,
                name: 'Atendimento',
                component: Atendimento,
            },
            {
                path: `${hosts.app}/chat`,
                name: 'Chat',
                component: Chat,
            },
            {
                path: `${hosts.app}/Chat_OLD`,
                name: 'Chat_OLD',
                component: Chat_OLD,
            },
            {
                path: `${hosts.app}/motivos-atendimento`,
                name: 'cadMotivosAtendimento',
                component: cadMotivosAtendimento,
            },
            {
                path: `${hosts.app}/fluxo-atendimento/:nr_controle?`,
                name: 'FluxoAtendimento',
                component: FluxoAtendimento,
            },
            {
                path: `${hosts.app}/listaFluxos`,
                name: 'listaFluxos',
                component: listaFluxos,
            },
            {
                path: `${hosts.app}/listaClientes`,
                name: 'listaClientes',
                component: listaClientes,
            },
            {
                path: `${hosts.app}/listaContatos`,
                name: 'listaContatos',
                component: listaContatos,
            },
            {
                path: `${hosts.app}/listaMensagem`,
                name: 'listaMensagem',
                component: listaMensagem,
            },
            {
                path: `${hosts.app}/listaMensagemPadrao/:padrao?`,
                name: 'listaMensagemPadrao',
                component: listaMensagemPadrao,
            },
            {
                path: `${hosts.app}/apiDocs`,
                name: 'apiDocs',
                component: apiDocs,
            },
            {
                path: `${hosts.app}/integracao`,
                name: 'Integracao',
                component: Integracao,
            },
            {
                path: `${hosts.app}/relMensagens`,
                name: 'relMensagens',
                component: relMensagens,
            },
            {
                path: `${hosts.app}/dashboard`,
                name: 'dashboard',
                component: Dashboard,
            },
            {
                path: `${hosts.app}/caixa`,
                name: 'listaCaixa',
                component: listaCaixa,
            },
            {
                path: `${hosts.app}/dash-geral`,
                name: 'dashGeral',
                component: dashGeral,
            },

            ...routerAdministracao,
            ...routerCRM,
        ],
    },
];

const router = createRouter({
    history: createWebHistory(),
    base: '/oizap/', // Altere para refletir o caminho correto no servidor
    routes,
    scrollBehavior(to, from, savedPosition) {
        return savedPosition || { left: 0, top: 0 };
    },
});

export default router;
