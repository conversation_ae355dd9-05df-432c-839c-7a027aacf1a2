require('dotenv').config();
import { Request } from 'express';
import { IRetorno, erroInterno } from '../../interfaces/IRetorno';
import { PostgreSQLServices, OperationObject } from '../../services/PostgreSQLServices';

export class UsuariosPerfisDB {
  static async incluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['usuarios_perfis'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async remover(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['usuarios_perfis'],
        chaves: { id: req.body.id },
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async removerPorUsuarioEPerfil(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['usuarios_perfis'],
        chaves: { 
          cd_usuario: req.body.cd_usuario,
          cd_perfil: req.body.cd_perfil 
        },
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listar(req: Request): Promise<IRetorno> {
    try {
      let sql = `
        SELECT 
          up.id,
          up.cd_usuario,
          up.cd_perfil,
          up.created_at,
          up.updated_at,
          u.ds_nome,
          u.ds_login,
          u.ds_email,
          p.nm_perfil
        FROM usuarios_perfis up
        JOIN adm_usuarios u ON up.cd_usuario = u.cd_usuario
        JOIN perfis p ON up.cd_perfil = p.cd_perfil
        WHERE 1=1
      `;

      if (req.query.cd_usuario) {
        sql += ` AND up.cd_usuario = '${req.query.cd_usuario}'`;
      }
      if (req.query.cd_perfil) {
        sql += ` AND up.cd_perfil = '${req.query.cd_perfil}'`;
      }
      if (req.query.ds_nome) {
        sql += ` AND LOWER(u.ds_nome) LIKE LOWER('%${req.query.ds_nome}%')`;
      }
      if (req.query.nm_perfil) {
        sql += ` AND LOWER(p.nm_perfil) LIKE LOWER('%${req.query.nm_perfil}%')`;
      }

      sql += ` ORDER BY u.ds_nome, p.nm_perfil`;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async buscarPorId(req: Request): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          up.id,
          up.cd_usuario,
          up.cd_perfil,
          up.created_at,
          up.updated_at,
          u.ds_nome,
          u.ds_login,
          u.ds_email,
          p.nm_perfil
        FROM usuarios_perfis up
        JOIN adm_usuarios u ON up.cd_usuario = u.cd_usuario
        JOIN perfis p ON up.cd_perfil = p.cd_perfil
        WHERE up.id = '${req.params.id}'
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async verificarExistencia(cd_usuario: number, cd_perfil: number): Promise<IRetorno> {
    try {
      const sql = `
        SELECT id
        FROM usuarios_perfis
        WHERE cd_usuario = '${cd_usuario}' AND cd_perfil = '${cd_perfil}'
        LIMIT 1
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listarPerfisPorUsuario(cd_usuario: number): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          p.cd_perfil,
          p.nm_perfil,
          up.created_at as data_associacao
        FROM usuarios_perfis up
        JOIN perfis p ON up.cd_perfil = p.cd_perfil
        WHERE up.cd_usuario = '${cd_usuario}'
        ORDER BY p.nm_perfil
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listarUsuariosSemPerfil(req: Request): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          u.cd_usuario,
          u.ds_nome,
          u.ds_login,
          u.ds_email
        FROM adm_usuarios u
        LEFT JOIN usuarios_perfis up ON u.cd_usuario = up.cd_usuario
        WHERE up.cd_usuario IS NULL
        ORDER BY u.ds_nome
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
