import { Request } from 'express';
import { IRetorno, erroInterno, sucesso, conflito, dadosNaoEncontrados, parametrosInvalidos } from '../../interfaces/IRetorno';
import { PerfisDB } from '../../data/perfis/PerfisDB';
import { UsuariosPerfisDB } from '../../data/perfis/UsuariosPerfisDB';
import { PerfisTelaDB } from '../../data/perfis/PerfisTelaDB';

export class PerfisModel {
  async incluir(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.nm_perfil) {
        return parametrosInvalidos(['O campo "nm_perfil" é obrigatório']);
      }

      if (req.body.nm_perfil.length < 3) {
        return parametrosInvalidos(['O nome do perfil deve ter pelo menos 3 caracteres']);
      }

      // Verificar se já existe um perfil com o mesmo nome
      req.query = { nm_perfil: req.body.nm_perfil };
      const perfilExistente = await PerfisDB.listar(req);
      
      if (perfilExistente.statuscode === 200 && perfilExistente.data.length > 0) {
        return conflito('Já existe um perfil com este nome');
      }

      // Incluir o perfil
      const resultado = await PerfisDB.incluir(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Perfil criado com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async alterar(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_perfil) {
        return parametrosInvalidos(['O campo "cd_perfil" é obrigatório']);
      }

      if (!req.body.nm_perfil) {
        return parametrosInvalidos(['O campo "nm_perfil" é obrigatório']);
      }

      if (req.body.nm_perfil.length < 3) {
        return parametrosInvalidos(['O nome do perfil deve ter pelo menos 3 caracteres']);
      }

      // Verificar se o perfil existe
      req.params = { cd_perfil: req.body.cd_perfil };
      const perfilExistente = await PerfisDB.buscarPorId(req);
      
      if (perfilExistente.statuscode !== 200 || perfilExistente.data.length === 0) {
        return dadosNaoEncontrados('Perfil não encontrado');
      }

      // Verificar se já existe outro perfil com o mesmo nome
      req.query = { nm_perfil: req.body.nm_perfil };
      const outroPerfilComMesmoNome = await PerfisDB.listar(req);
      
      if (outroPerfilComMesmoNome.statuscode === 200 && outroPerfilComMesmoNome.data.length > 0) {
        const perfilEncontrado = outroPerfilComMesmoNome.data[0];
        if (perfilEncontrado.cd_perfil !== parseInt(req.body.cd_perfil)) {
          return conflito('Já existe outro perfil com este nome');
        }
      }

      // Atualizar o perfil
      const resultado = await PerfisDB.alterar(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Perfil atualizado com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async remover(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_perfil) {
        return parametrosInvalidos(['O campo "cd_perfil" é obrigatório']);
      }

      // Verificar se o perfil existe
      req.params = { cd_perfil: req.body.cd_perfil };
      const perfilExistente = await PerfisDB.buscarPorId(req);
      
      if (perfilExistente.statuscode !== 200 || perfilExistente.data.length === 0) {
        return dadosNaoEncontrados('Perfil não encontrado');
      }

      // Verificar se há usuários associados ao perfil
      const usuariosAssociados = await PerfisDB.listarUsuariosPorPerfil(req);
      
      if (usuariosAssociados.statuscode === 200 && usuariosAssociados.data.length > 0) {
        return conflito('Não é possível remover o perfil pois há usuários associados a ele');
      }

      // Remover o perfil (as associações com telas serão removidas automaticamente pelo CASCADE)
      const resultado = await PerfisDB.remover(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Perfil removido com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listar(req: Request): Promise<IRetorno> {
    try {
      const resultado = await PerfisDB.listar(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Perfis listados com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async buscarPorId(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.params.cd_perfil) {
        return parametrosInvalidos(['O parâmetro "cd_perfil" é obrigatório']);
      }

      const resultado = await PerfisDB.buscarPorId(req);
      
      if (resultado.statuscode === 200) {
        if (resultado.data.length === 0) {
          return dadosNaoEncontrados('Perfil não encontrado');
        }
        return sucesso('Perfil encontrado', resultado.data[0]);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listarUsuarios(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.params.cd_perfil) {
        return parametrosInvalidos(['O parâmetro "cd_perfil" é obrigatório']);
      }

      const resultado = await PerfisDB.listarUsuariosPorPerfil(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Usuários do perfil listados com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listarTelas(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.params.cd_perfil) {
        return parametrosInvalidos(['O parâmetro "cd_perfil" é obrigatório']);
      }

      const resultado = await PerfisDB.listarTelasPorPerfil(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Telas do perfil listadas com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
