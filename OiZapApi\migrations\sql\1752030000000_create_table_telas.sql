-- Migration: create_table_telas
-- Created: 2025-01-09T02:40:00.000Z
-- Environment: ALL

-- ========================================
-- UP: Criar/Alterar estrutura
-- ========================================

-- Criar tabela telas
CREATE TABLE IF NOT EXISTS telas (
  cd_tela SERIAL PRIMARY KEY,
  nm_tela VARCHAR(100) NOT NULL,
  ds_rota VARCHAR(255) NOT NULL UNIQUE,
  ds_descricao TEXT,
  in_ativa BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Comentários nas tabelas e colunas
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'telas') THEN
    COMMENT ON TABLE telas IS 'Tabela de telas/páginas do sistema para controle de acesso';
    COMMENT ON COLUMN telas.cd_tela IS 'Código único da tela';
    COMMENT ON COLUMN telas.nm_tela IS 'Nome da tela/página';
    COMMENT ON COLUMN telas.ds_rota IS 'Rota/URL da tela no frontend';
    COMMENT ON COLUMN telas.ds_descricao IS 'Descrição da funcionalidade da tela';
    COMMENT ON COLUMN telas.in_ativa IS 'Indica se a tela está ativa no sistema';
    COMMENT ON COLUMN telas.created_at IS 'Data de criação do registro';
    COMMENT ON COLUMN telas.updated_at IS 'Data da última atualização';
  END IF;
END $$;

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_telas_rota 
  ON telas(ds_rota);

CREATE INDEX IF NOT EXISTS idx_telas_ativa 
  ON telas(in_ativa);

CREATE INDEX IF NOT EXISTS idx_telas_nome 
  ON telas(nm_tela);

-- Inserir algumas telas básicas do sistema
INSERT INTO telas (nm_tela, ds_rota, ds_descricao) VALUES
  ('Dashboard', '/dashboard', 'Página principal do sistema'),
  ('Usuários', '/usuarios', 'Gestão de usuários do sistema'),
  ('Perfis', '/perfis', 'Gestão de perfis de acesso'),
  ('Telas', '/telas', 'Gestão de telas do sistema'),
  ('Estabelecimentos', '/estabelecimentos', 'Gestão de estabelecimentos'),
  ('Produtos', '/produtos', 'Gestão de produtos'),
  ('Pedidos', '/pedidos', 'Gestão de pedidos'),
  ('Atendimentos', '/atendimentos', 'Gestão de atendimentos'),
  ('Relatórios', '/relatorios', 'Relatórios do sistema'),
  ('Configurações', '/configuracoes', 'Configurações gerais do sistema')
ON CONFLICT (ds_rota) DO NOTHING;

-- ========================================
-- Adicione seu SQL personalizado aqui:
-- ========================================
