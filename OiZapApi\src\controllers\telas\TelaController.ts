import { Request, Response } from 'express';
import { BAD_REQUEST, INTERNAL_SERVER_ERROR, parametrosInvalidos, erroInterno } from '../../interfaces/IRetorno';
import { TelasModel } from '../../models/telas/TelasModel';

export class TelaController {
  static async criarTela(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.nm_tela) {
        errors.push('O campo "nm_tela" é obrigatório.');
      }
      
      if (!req.body.ds_rota) {
        errors.push('O campo "ds_rota" é obrigatório.');
      }
      
      if (req.body.nm_tela && req.body.nm_tela.length < 3) {
        errors.push('O nome da tela deve ter pelo menos 3 caracteres.');
      }
      
      if (req.body.ds_rota && !req.body.ds_rota.startsWith('/')) {
        errors.push('A rota deve começar com "/".');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new TelasModel().incluir(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async atualizarTela(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.cd_tela && !req.params.cd_tela) {
        errors.push('O campo "cd_tela" é obrigatório.');
      }
      
      if (!req.body.nm_tela) {
        errors.push('O campo "nm_tela" é obrigatório.');
      }
      
      if (!req.body.ds_rota) {
        errors.push('O campo "ds_rota" é obrigatório.');
      }
      
      if (req.body.nm_tela && req.body.nm_tela.length < 3) {
        errors.push('O nome da tela deve ter pelo menos 3 caracteres.');
      }
      
      if (req.body.ds_rota && !req.body.ds_rota.startsWith('/')) {
        errors.push('A rota deve começar com "/".');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      // Se cd_tela veio nos params, usar ele
      if (req.params.cd_tela && !req.body.cd_tela) {
        req.body.cd_tela = req.params.cd_tela;
      }

      const result = await new TelasModel().alterar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async deletarTela(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.cd_tela && !req.params.cd_tela) {
        errors.push('O campo "cd_tela" é obrigatório.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      // Se cd_tela veio nos params, usar ele
      if (req.params.cd_tela && !req.body.cd_tela) {
        req.body.cd_tela = req.params.cd_tela;
      }

      const result = await new TelasModel().remover(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async listarTelas(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new TelasModel().listar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async listarTelasAgrupadasPorModulo(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new TelasModel().listarAgrupadasPorModulo(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async buscarTelaPorId(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.params.cd_tela) {
        errors.push('O parâmetro "cd_tela" é obrigatório.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new TelasModel().buscarPorId(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async listarPerfisDaTela(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.params.cd_tela) {
        errors.push('O parâmetro "cd_tela" é obrigatório.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new TelasModel().listarPerfis(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async verificarAcesso(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.params.cd_usuario) {
        errors.push('O parâmetro "cd_usuario" é obrigatório.');
      }
      
      if (!req.params.ds_rota) {
        errors.push('O parâmetro "ds_rota" é obrigatório.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new TelasModel().verificarAcesso(
        parseInt(req.params.cd_usuario), 
        req.params.ds_rota
      );
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async ativarDesativarTela(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.cd_tela && !req.params.cd_tela) {
        errors.push('O campo "cd_tela" é obrigatório.');
      }
      
      if (req.body.in_ativa === undefined) {
        errors.push('O campo "in_ativa" é obrigatório.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      // Se cd_tela veio nos params, usar ele
      if (req.params.cd_tela && !req.body.cd_tela) {
        req.body.cd_tela = req.params.cd_tela;
      }

      const result = await new TelasModel().ativarDesativar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
