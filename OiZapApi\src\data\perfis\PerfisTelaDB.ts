require('dotenv').config();
import { Request } from 'express';
import { IRetorno, erroInterno } from '../../interfaces/IRetorno';
import { PostgreSQLServices, OperationObject } from '../../services/PostgreSQLServices';

export class PerfisTelaDB {
  static async incluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['perfis_telas'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async alterar(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['perfis_telas'],
        chaves: { nr_perfil_tela: req.body.nr_perfil_tela },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async remover(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['perfis_telas'],
        chaves: { nr_perfil_tela: req.body.nr_perfil_tela },
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async removerPorPerfilETela(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['perfis_telas'],
        chaves: { 
          cd_perfil: req.body.cd_perfil,
          cd_tela: req.body.cd_tela 
        },
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listar(req: Request): Promise<IRetorno> {
    try {
      let sql = `
        SELECT 
          pt.nr_perfil_tela,
          pt.cd_perfil,
          pt.cd_tela,
          pt.in_visualizar,
          pt.in_inserir,
          pt.in_alterar,
          pt.in_excluir,
          pt.created_at,
          p.nm_perfil,
          t.nm_tela,
          t.ds_rota,
          t.ds_descricao
        FROM perfis_telas pt
        JOIN perfis p ON pt.cd_perfil = p.cd_perfil
        JOIN telas t ON pt.cd_tela = t.cd_tela
        WHERE 1=1
      `;

      if (req.query.cd_perfil) {
        sql += ` AND pt.cd_perfil = '${req.query.cd_perfil}'`;
      }
      if (req.query.cd_tela) {
        sql += ` AND pt.cd_tela = '${req.query.cd_tela}'`;
      }
      if (req.query.nm_perfil) {
        sql += ` AND LOWER(p.nm_perfil) LIKE LOWER('%${req.query.nm_perfil}%')`;
      }
      if (req.query.nm_tela) {
        sql += ` AND LOWER(t.nm_tela) LIKE LOWER('%${req.query.nm_tela}%')`;
      }

      sql += ` ORDER BY p.nm_perfil, t.nm_tela`;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async buscarPorId(req: Request): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          pt.nr_perfil_tela,
          pt.cd_perfil,
          pt.cd_tela,
          pt.in_visualizar,
          pt.in_inserir,
          pt.in_alterar,
          pt.in_excluir,
          pt.created_at,
          p.nm_perfil,
          t.nm_tela,
          t.ds_rota,
          t.ds_descricao
        FROM perfis_telas pt
        JOIN perfis p ON pt.cd_perfil = p.cd_perfil
        JOIN telas t ON pt.cd_tela = t.cd_tela
        WHERE pt.nr_perfil_tela = '${req.params.nr_perfil_tela}'
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async verificarExistencia(cd_perfil: number, cd_tela: number): Promise<IRetorno> {
    try {
      const sql = `
        SELECT nr_perfil_tela
        FROM perfis_telas
        WHERE cd_perfil = '${cd_perfil}' AND cd_tela = '${cd_tela}'
        LIMIT 1
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async atualizarPermissoes(req: Request): Promise<IRetorno> {
    try {
      const { cd_perfil, cd_tela, in_visualizar, in_inserir, in_alterar, in_excluir } = req.body;
      
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['perfis_telas'],
        chaves: { cd_perfil, cd_tela },
        dados: { in_visualizar, in_inserir, in_alterar, in_excluir },
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
