import { Router } from 'express';
import authApi from '../../middleware/authApi';
import { PerfilController } from '../../controllers/perfis/PerfilController';

const router = Router();

// Rotas para gestão de perfis
router.post('/oizap/perfis', authApi, PerfilController.criarPerfil);
router.get('/oizap/perfis', authApi, PerfilController.listarPerfis);
router.get('/oizap/perfis/:cd_perfil', authApi, PerfilController.buscarPerfilPorId);
router.put('/oizap/perfis/:cd_perfil', authApi, PerfilController.atualizarPerfil);
router.delete('/oizap/perfis/:cd_perfil', authApi, PerfilController.deletarPerfil);

// Rotas para listar relacionamentos do perfil
router.get('/oizap/perfis/:cd_perfil/usuarios', authApi, PerfilController.listarUsuariosDoPerfil);
router.get('/oizap/perfis/:cd_perfil/telas', authApi, PerfilController.listarTelasDoPerfil);

export { router as PerfisRoutes };
