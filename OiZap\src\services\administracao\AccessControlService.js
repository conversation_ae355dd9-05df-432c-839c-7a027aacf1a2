import { callApi } from '@/utils/ApiService';
import hosts from '@/utils/hosts';

const hostApi = hosts.apiOiZap;

export default {
    // ===== PERFIS =====
    async listarPerfis(params = {}) {
        try {
            const response = await callApi('get', `${hostApi}/perfis`, params);
            return response;
        } catch (error) {
            console.error('Erro ao listar perfis:', error);
            throw error;
        }
    },

    async buscarPerfilPorId(cd_perfil) {
        try {
            const response = await callApi('get', `${hostApi}/perfis/${cd_perfil}`);
            return response;
        } catch (error) {
            console.error('Erro ao buscar perfil:', error);
            throw error;
        }
    },

    async criarPerfil(dados) {
        try {
            const response = await callApi('post', `${hostApi}/perfis`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao criar perfil:', error);
            throw error;
        }
    },

    async atualizarPerfil(cd_perfil, dados) {
        try {
            const response = await callApi('put', `${hostApi}/perfis/${cd_perfil}`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao atualizar perfil:', error);
            throw error;
        }
    },

    async deletarPerfil(cd_perfil) {
        try {
            const response = await callApi('delete', `${hostApi}/perfis/${cd_perfil}`, undefined, { cd_perfil });
            return response;
        } catch (error) {
            console.error('Erro ao deletar perfil:', error);
            throw error;
        }
    },

    // ===== TELAS =====
    async listarTelas(params = {}) {
        try {
            const response = await callApi('get', `${hostApi}/telas`, params);
            return response;
        } catch (error) {
            console.error('Erro ao listar telas:', error);
            throw error;
        }
    },

    async buscarTelaPorId(cd_tela) {
        try {
            const response = await callApi('get', `${hostApi}/telas/${cd_tela}`);
            return response;
        } catch (error) {
            console.error('Erro ao buscar tela:', error);
            throw error;
        }
    },

    async criarTela(dados) {
        try {
            const response = await callApi('post', `${hostApi}/telas`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao criar tela:', error);
            throw error;
        }
    },

    async atualizarTela(cd_tela, dados) {
        try {
            const response = await callApi('put', `${hostApi}/telas/${cd_tela}`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao atualizar tela:', error);
            throw error;
        }
    },

    async deletarTela(cd_tela) {
        try {
            const response = await callApi('delete', `${hostApi}/telas/${cd_tela}`, undefined, { cd_tela });
            return response;
        } catch (error) {
            console.error('Erro ao deletar tela:', error);
            throw error;
        }
    },

    // ===== ASSOCIAÇÕES PERFIL-TELA =====
    async listarAssociacoesPerfisTelasPorPerfil(cd_perfil) {
        try {
            const response = await callApi('get', `${hostApi}/perfis-telas`, { cd_perfil });
            return response;
        } catch (error) {
            console.error('Erro ao listar associações perfil-tela:', error);
            throw error;
        }
    },

    async associarPerfilTela(dados) {
        try {
            const response = await callApi('post', `${hostApi}/perfis-telas`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao associar perfil-tela:', error);
            throw error;
        }
    },

    async desassociarPerfilTela(cd_perfil, cd_tela) {
        try {
            const response = await callApi('delete', `${hostApi}/perfis-telas`, undefined, { cd_perfil, cd_tela });
            return response;
        } catch (error) {
            console.error('Erro ao desassociar perfil-tela:', error);
            throw error;
        }
    },

    async atualizarPermissoes(dados) {
        try {
            const response = await callApi('put', `${hostApi}/perfis-telas/permissoes`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao atualizar permissões:', error);
            throw error;
        }
    },

    // ===== ASSOCIAÇÕES USUÁRIO-PERFIL =====
    async listarPerfisPorUsuario(cd_usuario) {
        try {
            const response = await callApi('get', `${hostApi}/usuarios/${cd_usuario}/perfis`);
            return response;
        } catch (error) {
            console.error('Erro ao listar perfis do usuário:', error);
            throw error;
        }
    },

    async listarAssociacoesUsuariosPerfis(params = {}) {
        try {
            const response = await callApi('get', `${hostApi}/usuarios-perfis`, params);
            return response;
        } catch (error) {
            console.error('Erro ao listar associações usuário-perfil:', error);
            throw error;
        }
    },

    async associarUsuarioPerfil(dados) {
        try {
            const response = await callApi('post', `${hostApi}/usuarios-perfis`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao associar usuário-perfil:', error);
            throw error;
        }
    },

    async desassociarUsuarioPerfil(cd_usuario, cd_perfil) {
        try {
            const response = await callApi('delete', `${hostApi}/usuarios-perfis`, undefined, { cd_usuario, cd_perfil });
            return response;
        } catch (error) {
            console.error('Erro ao desassociar usuário-perfil:', error);
            throw error;
        }
    },

    async substituirPerfisUsuario(cd_usuario, perfis) {
        try {
            const response = await callApi('put', `${hostApi}/usuarios-perfis/substituir`, undefined, { cd_usuario, perfis });
            return response;
        } catch (error) {
            console.error('Erro ao substituir perfis do usuário:', error);
            throw error;
        }
    },

    async listarUsuarios(params = {}) {
        try {
            // Usar a rota existente do sistema
            const response = await callApi('get', `${hostApi.replace('/oizap', '')}/admin/usuario/listaUsuarios/v1`, params);
            return response;
        } catch (error) {
            console.error('Erro ao listar usuários:', error);
            throw error;
        }
    },

    // ===== VERIFICAÇÃO DE ACESSO =====
    async verificarAcesso(cd_usuario, ds_rota) {
        try {
            const response = await callApi('get', `${hostApi}/telas/acesso/${cd_usuario}/${encodeURIComponent(ds_rota)}`);
            return response;
        } catch (error) {
            console.error('Erro ao verificar acesso:', error);
            throw error;
        }
    },

    // ===== MENU BASEADO EM PERFIS =====
    async obterMenuPorUsuario(cd_usuario) {
        try {
            console.log('🔍 AccessControlService: Obtendo menu para usuário', cd_usuario);

            // Buscar perfis do usuário
            const perfisResponse = await this.listarPerfisPorUsuario(cd_usuario);
            console.log('📋 Perfis do usuário:', perfisResponse);

            if (perfisResponse.statuscode !== 200 || !perfisResponse.data?.length) {
                console.log('❌ Nenhum perfil encontrado para o usuário');
                return { menu: [], telasPermitidas: [] };
            }

            // Buscar telas associadas aos perfis
            const telasPermitidas = new Map();

            for (const perfil of perfisResponse.data) {
                console.log('🔍 Buscando associações para perfil:', perfil.cd_perfil);
                const associacoesResponse = await this.listarAssociacoesPerfisTelasPorPerfil(perfil.cd_perfil);
                console.log('🔗 Associações encontradas:', associacoesResponse);

                if (associacoesResponse.statuscode === 200 && associacoesResponse.data) {
                    associacoesResponse.data.forEach(associacao => {
                        const key = associacao.tela?.ds_rota || associacao.ds_rota;
                        if (key && (!telasPermitidas.has(key) ||
                            telasPermitidas.get(key).permissoes.in_visualizar < associacao.in_visualizar)) {
                            telasPermitidas.set(key, {
                                tela: associacao.tela || associacao,
                                permissoes: {
                                    in_visualizar: associacao.in_visualizar,
                                    in_inserir: associacao.in_inserir,
                                    in_alterar: associacao.in_alterar,
                                    in_excluir: associacao.in_excluir
                                }
                            });
                        }
                    });
                }
            }

            console.log('🎯 Telas permitidas processadas:', Array.from(telasPermitidas.values()));

            // Construir menu baseado nas telas permitidas
            const menu = this.construirMenuPorTelas(Array.from(telasPermitidas.values()));
            console.log('📱 Menu construído:', menu);

            return { menu, telasPermitidas: Array.from(telasPermitidas.values()) };
        } catch (error) {
            console.error('❌ Erro ao obter menu por usuário:', error);
            return { menu: [], telasPermitidas: [] };
        }
    },

    construirMenuPorTelas(telasPermitidas) {
        const menu = [];
        
        // Mapear telas para itens de menu
        const menuMap = {
            '/oizap/perfis': {
                icon: 'UsersIcon',
                pageName: 'gestao-perfis',
                title: 'Gestão de Perfis',
                desc: 'Gerencie perfis de acesso'
            },
            '/oizap/telas': {
                icon: 'LayoutIcon',
                pageName: 'gestao-telas', 
                title: 'Gestão de Telas',
                desc: 'Gerencie telas do sistema'
            },
            '/oizap/perfis-telas': {
                icon: 'LinkIcon',
                pageName: 'gestao-perfis-telas',
                title: 'Perfis & Telas',
                desc: 'Associe perfis às telas'
            },
            '/oizap/usuarios-perfis': {
                icon: 'UserCheckIcon',
                pageName: 'gestao-usuarios-perfis',
                title: 'Usuários & Perfis', 
                desc: 'Associe usuários aos perfis'
            }
        };

        // Verificar se há telas administrativas
        const telasAdmin = telasPermitidas.filter(item => 
            menuMap[item.tela.ds_rota] && item.permissoes.in_visualizar
        );

        if (telasAdmin.length > 0) {
            menu.push({
                icon: 'SettingsIcon',
                title: 'Administração',
                subMenu: telasAdmin.map(item => ({
                    ...menuMap[item.tela.ds_rota],
                    permissoes: item.permissoes
                }))
            });
        }

        return menu;
    }
};
