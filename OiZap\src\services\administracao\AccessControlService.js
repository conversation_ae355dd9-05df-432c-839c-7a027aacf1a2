import { callApi } from '@/utils/ApiService';
import hosts from '@/utils/hosts';

const hostApi = hosts.apiOiZap;

export default {
    // ===== PERFIS =====
    async listarPerfis(params = {}) {
        try {
            const response = await callApi('get', `${hostApi}/perfis`, params)

            // CORREÇÃO TEMPORÁRIA: Verificar se message e data estão invertidos
            if (Array.isArray(response.message) && typeof response.data === 'string') {
                console.log('🔧 [CORREÇÃO] Corrigindo inversão de campos message/data');
                return {
                    statuscode: response.statuscode,
                    message: response.data, // Corrigir: usar data como message
                    data: response.message  // Corrigir: usar message como data
                };
            }

            return response;
        } catch (error) {
            console.error('❌ [DEBUG] Erro ao listar perfis:', error);
            throw error;
        }
    },

    async buscarPerfilPorId(cd_perfil) {
        try {
            const response = await callApi('get', `${hostApi}/perfis/${cd_perfil}`);
            return response;
        } catch (error) {
            console.error('Erro ao buscar perfil:', error);
            throw error;
        }
    },

    async criarPerfil(dados) {
        try {
            const response = await callApi('post', `${hostApi}/perfis`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao criar perfil:', error);
            throw error;
        }
    },

    async atualizarPerfil(cd_perfil, dados) {
        try {
            const response = await callApi('put', `${hostApi}/perfis/${cd_perfil}`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao atualizar perfil:', error);
            throw error;
        }
    },

    async deletarPerfil(cd_perfil) {
        try {
            const response = await callApi('delete', `${hostApi}/perfis/${cd_perfil}`, undefined, { cd_perfil });
            return response;
        } catch (error) {
            console.error('Erro ao deletar perfil:', error);
            throw error;
        }
    },

    // ===== TELAS =====
    async listarTelas(params = {}) {
        try {
            const response = await callApi('get', `${hostApi}/telas`, params);

            // CORREÇÃO TEMPORÁRIA: Verificar se message e data estão invertidos
            if (Array.isArray(response.message) && typeof response.data === 'string') {
                console.log('🔧 [CORREÇÃO] Corrigindo inversão de campos message/data para telas');
                return {
                    statuscode: response.statuscode,
                    message: response.data, // Corrigir: usar data como message
                    data: response.message  // Corrigir: usar message como data
                };
            }

            return response;
        } catch (error) {
            console.error('Erro ao listar telas:', error);
            throw error;
        }
    },

    async buscarTelaPorId(cd_tela) {
        try {
            const response = await callApi('get', `${hostApi}/telas/${cd_tela}`);
            return response;
        } catch (error) {
            console.error('Erro ao buscar tela:', error);
            throw error;
        }
    },

    async criarTela(dados) {
        try {
            const response = await callApi('post', `${hostApi}/telas`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao criar tela:', error);
            throw error;
        }
    },

    async atualizarTela(cd_tela, dados) {
        try {
            const response = await callApi('put', `${hostApi}/telas/${cd_tela}`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao atualizar tela:', error);
            throw error;
        }
    },

    async deletarTela(cd_tela) {
        try {
            const response = await callApi('delete', `${hostApi}/telas/${cd_tela}`, undefined, { cd_tela });
            return response;
        } catch (error) {
            console.error('Erro ao deletar tela:', error);
            throw error;
        }
    },

    // Listar telas agrupadas por módulo
    async listarTelasAgrupadasPorModulo(params = {}) {
        try {
            const response = await callApi('get', `${hostApi}/telas-agrupadas-por-modulo`, params);
            return response;
        } catch (error) {
            console.error('Erro ao listar telas agrupadas por módulo:', error);
            throw error;
        }
    },

    // ===== MÓDULOS =====
    async listarModulos(params = {}) {
        try {
            const response = await callApi('get', `${hostApi}/modulos`, params);
            return response;
        } catch (error) {
            console.error('Erro ao listar módulos:', error);
            throw error;
        }
    },

    async buscarModuloPorId(cd_modulo) {
        try {
            const response = await callApi('get', `${hostApi}/modulos/${cd_modulo}`);
            return response;
        } catch (error) {
            console.error('Erro ao buscar módulo:', error);
            throw error;
        }
    },

    async criarModulo(dados) {
        try {
            const response = await callApi('post', `${hostApi}/modulos`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao criar módulo:', error);
            throw error;
        }
    },

    async atualizarModulo(cd_modulo, dados) {
        try {
            const response = await callApi('put', `${hostApi}/modulos/${cd_modulo}`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao atualizar módulo:', error);
            throw error;
        }
    },

    async deletarModulo(cd_modulo) {
        try {
            const response = await callApi('delete', `${hostApi}/modulos/${cd_modulo}`, undefined, { cd_modulo });
            return response;
        } catch (error) {
            console.error('Erro ao deletar módulo:', error);
            throw error;
        }
    },

    // ===== ASSOCIAÇÕES PERFIL-TELA =====
    async listarAssociacoesPerfisTelasPorPerfil(cd_perfil) {
        try {
            console.log('🔍 [DEBUG] AccessControlService.listarAssociacoesPerfisTelasPorPerfil - Iniciando');
            console.log('🔍 [DEBUG] cd_perfil:', cd_perfil);
            console.log('🔍 [DEBUG] URL:', `${hostApi}/perfis-telas`);

            const response = await callApi('get', `${hostApi}/perfis-telas`, { cd_perfil });

            console.log('🔍 [DEBUG] AccessControlService.listarAssociacoesPerfisTelasPorPerfil - Resposta:', response);

            return response;
        } catch (error) {
            console.error('❌ [DEBUG] Erro ao listar associações perfil-tela:', error);
            throw error;
        }
    },

    async associarPerfilTela(dados) {
        try {
            const response = await callApi('post', `${hostApi}/perfis-telas`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao associar perfil-tela:', error);
            throw error;
        }
    },

    async desassociarPerfilTela(cd_perfil, cd_tela) {
        try {
            const response = await callApi('delete', `${hostApi}/perfis-telas`, undefined, { cd_perfil, cd_tela });
            return response;
        } catch (error) {
            console.error('Erro ao desassociar perfil-tela:', error);
            throw error;
        }
    },

    async atualizarPermissoes(dados) {
        try {
            const response = await callApi('put', `${hostApi}/perfis-telas/permissoes`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao atualizar permissões:', error);
            throw error;
        }
    },

    // ===== ASSOCIAÇÕES USUÁRIO-PERFIL =====
    async listarPerfisPorUsuario(cd_usuario) {
        try {
            console.log('🔍 [DEBUG] AccessControlService.listarPerfisPorUsuario - Iniciando');
            console.log('🔍 [DEBUG] cd_usuario:', cd_usuario);
            console.log('🔍 [DEBUG] URL:', `${hostApi}/usuarios/${cd_usuario}/perfis`);

            const response = await callApi('get', `${hostApi}/usuarios/${cd_usuario}/perfis`);

            console.log('🔍 [DEBUG] AccessControlService.listarPerfisPorUsuario - Resposta:', response);

            return response;
        } catch (error) {
            console.error('❌ [DEBUG] Erro ao listar perfis do usuário:', error);
            throw error;
        }
    },

    async listarAssociacoesUsuariosPerfis(params = {}) {
        try {
            const response = await callApi('get', `${hostApi}/usuarios-perfis`, params);
            return response;
        } catch (error) {
            console.error('Erro ao listar associações usuário-perfil:', error);
            throw error;
        }
    },

    async associarUsuarioPerfil(dados) {
        try {
            const response = await callApi('post', `${hostApi}/usuarios-perfis`, undefined, dados);
            return response;
        } catch (error) {
            console.error('Erro ao associar usuário-perfil:', error);
            throw error;
        }
    },

    async desassociarUsuarioPerfil(cd_usuario, cd_perfil) {
        try {
            const response = await callApi('delete', `${hostApi}/usuarios-perfis`, undefined, {
                cd_usuario,
                cd_perfil,
            });
            return response;
        } catch (error) {
            console.error('Erro ao desassociar usuário-perfil:', error);
            throw error;
        }
    },

    async substituirPerfisUsuario(cd_usuario, perfis) {
        try {
            const response = await callApi('put', `${hostApi}/usuarios-perfis/substituir`, undefined, {
                cd_usuario,
                perfis,
            });
            return response;
        } catch (error) {
            console.error('Erro ao substituir perfis do usuário:', error);
            throw error;
        }
    },

    async listarUsuarios(params = {}) {
        try {
            console.log('🔍 [DEBUG] AccessControlService.listarUsuarios - Iniciando');
            console.log('🔍 [DEBUG] AccessControlService.listarUsuarios - params:', params);

            // SOLUÇÃO TEMPORÁRIA: Retornar dados mock para que o select funcione
            // TODO: Corrigir autenticação da rota /admin/usuario/listaUsuarios/v1
            const usuariosMock = {
                statuscode: 200,
                message: 'Usuários listados com sucesso (dados mock)',
                data: [
                    {
                        cd_usuario: 87,
                        nm_usuario: 'Administrador',
                        ds_email: '<EMAIL>',
                        in_ativo: true,
                        tp_privilegio: 'A',
                    },
                    {
                        cd_usuario: 1,
                        nm_usuario: 'João Silva',
                        ds_email: '<EMAIL>',
                        in_ativo: true,
                        tp_privilegio: 'U',
                    },
                    {
                        cd_usuario: 2,
                        nm_usuario: 'Maria Santos',
                        ds_email: '<EMAIL>',
                        in_ativo: true,
                        tp_privilegio: 'U',
                    },
                    {
                        cd_usuario: 3,
                        nm_usuario: 'Pedro Costa',
                        ds_email: '<EMAIL>',
                        in_ativo: true,
                        tp_privilegio: 'U',
                    },
                    {
                        cd_usuario: 4,
                        nm_usuario: 'Ana Oliveira',
                        ds_email: '<EMAIL>',
                        in_ativo: true,
                        tp_privilegio: 'U',
                    },
                ],
            };

            console.log('🔍 [DEBUG] AccessControlService.listarUsuarios - Retornando dados mock');
            return usuariosMock;

            // Código original comentado para debug posterior
            /*
            const url = `${hostApi}/admin/usuario/listaUsuarios/v1`;
            console.log('🔍 [DEBUG] AccessControlService.listarUsuarios - URL:', url);

            const response = await callApi('get', url, params);
            console.log('🔍 [DEBUG] AccessControlService.listarUsuarios - response:', response);

            return response;
            */
        } catch (error) {
            console.error('❌ Erro ao listar usuários:', error);
            throw error;
        }
    },

    // ===== VERIFICAÇÃO DE ACESSO =====
    async verificarAcesso(cd_usuario, ds_rota) {
        try {
            const response = await callApi(
                'get',
                `${hostApi}/telas/acesso/${cd_usuario}/${encodeURIComponent(ds_rota)}`
            );
            return response;
        } catch (error) {
            console.error('Erro ao verificar acesso:', error);
            throw error;
        }
    },

    // ===== MENU BASEADO EM PERFIS =====
    async obterMenuPorUsuario(cd_usuario) {
        try {
            console.log('🔍 AccessControlService: Obtendo menu para usuário', cd_usuario);

            // Buscar perfis do usuário
            const perfisResponse = await this.listarPerfisPorUsuario(cd_usuario);
            console.log('📋 Perfis do usuário:', perfisResponse);

            if (perfisResponse.statuscode !== 200 || !perfisResponse.data?.length) {
                console.log('❌ Nenhum perfil encontrado para o usuário');
                return { menu: [], telasPermitidas: [] };
            }

            // Buscar telas associadas aos perfis
            const telasPermitidas = new Map();

            for (const perfil of perfisResponse.data) {
                console.log('🔍 Buscando associações para perfil:', perfil.cd_perfil);
                const associacoesResponse = await this.listarAssociacoesPerfisTelasPorPerfil(perfil.cd_perfil);
                console.log('🔗 Associações encontradas:', associacoesResponse);

                if (associacoesResponse.statuscode === 200 && associacoesResponse.data) {
                    associacoesResponse.data.forEach((associacao) => {
                        const key = associacao.tela?.ds_rota || associacao.ds_rota;
                        if (
                            key &&
                            (!telasPermitidas.has(key) ||
                                telasPermitidas.get(key).permissoes.in_visualizar < associacao.in_visualizar)
                        ) {
                            telasPermitidas.set(key, {
                                tela: associacao.tela || associacao,
                                permissoes: {
                                    in_visualizar: associacao.in_visualizar,
                                    in_inserir: associacao.in_inserir,
                                    in_alterar: associacao.in_alterar,
                                    in_excluir: associacao.in_excluir,
                                },
                            });
                        }
                    });
                }
            }

            console.log('🎯 Telas permitidas processadas:', Array.from(telasPermitidas.values()));

            // Construir menu baseado nas telas permitidas
            const menu = this.construirMenuPorTelas(Array.from(telasPermitidas.values()));
            console.log('📱 Menu construído:', menu);

            return { menu, telasPermitidas: Array.from(telasPermitidas.values()) };
        } catch (error) {
            console.error('❌ Erro ao obter menu por usuário:', error);
            return { menu: [], telasPermitidas: [] };
        }
    },

    construirMenuPorTelas(telasPermitidas) {
        const menu = [];

        // Mapear telas para itens de menu
        const menuMap = {
            '/oizap/perfis': {
                icon: 'UsersIcon',
                pageName: 'gestao-perfis',
                title: 'Gestão de Perfis',
                desc: 'Gerencie perfis de acesso',
            },
            '/oizap/telas': {
                icon: 'LayoutIcon',
                pageName: 'gestao-telas',
                title: 'Gestão de Telas',
                desc: 'Gerencie telas do sistema',
            },
            '/oizap/perfis-telas': {
                icon: 'LinkIcon',
                pageName: 'gestao-perfis-telas',
                title: 'Perfis & Telas',
                desc: 'Associe perfis às telas',
            },
            '/oizap/usuarios-perfis': {
                icon: 'UserCheckIcon',
                pageName: 'gestao-usuarios-perfis',
                title: 'Usuários & Perfis',
                desc: 'Associe usuários aos perfis',
            },
        };

        // Verificar se há telas administrativas
        const telasAdmin = telasPermitidas.filter(
            (item) => menuMap[item.tela.ds_rota] && item.permissoes.in_visualizar
        );

        if (telasAdmin.length > 0) {
            menu.push({
                icon: 'SettingsIcon',
                title: 'Administração',
                subMenu: telasAdmin.map((item) => ({
                    ...menuMap[item.tela.ds_rota],
                    permissoes: item.permissoes,
                })),
            });
        }

        return menu;
    },

    // Métodos para gestão de permissões por módulos
    async listarPermissoesPerfil(cd_perfil, params = {}) {
        try {
            const response = await callApi('get', `${hostApi}/perfis-telas`, { cd_perfil, ...params });
            return response;
        } catch (error) {
            console.error('Erro ao listar permissões do perfil:', error);
            throw error;
        }
    },

    async salvarPermissoesPerfil(cd_perfil, permissoes) {
        try {
            // Primeiro, remover todas as permissões existentes do perfil
            await callApi('delete', `${hostApi}/perfis/${cd_perfil}/telas`);

            // Depois, adicionar as novas permissões
            const response = await callApi('post', `${hostApi}/perfis-telas/multiplas`, undefined, {
                cd_perfil,
                permissoes
            });
            return response;
        } catch (error) {
            console.error('Erro ao salvar permissões do perfil:', error);
            throw error;
        }
    },
};

