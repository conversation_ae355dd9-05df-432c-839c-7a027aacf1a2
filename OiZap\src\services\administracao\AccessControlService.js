import api from '@/utils/api';

export default {
    // ===== PERFIS =====
    async listarPerfis(params = {}) {
        try {
            const response = await api.get('/oizap/perfis', { params });
            return response.data;
        } catch (error) {
            console.error('Erro ao listar perfis:', error);
            throw error;
        }
    },

    async buscarPerfilPorId(cd_perfil) {
        try {
            const response = await api.get(`/oizap/perfis/${cd_perfil}`);
            return response.data;
        } catch (error) {
            console.error('Erro ao buscar perfil:', error);
            throw error;
        }
    },

    async criarPerfil(dados) {
        try {
            const response = await api.post('/oizap/perfis', dados);
            return response.data;
        } catch (error) {
            console.error('Erro ao criar perfil:', error);
            throw error;
        }
    },

    async atualizarPerfil(cd_perfil, dados) {
        try {
            const response = await api.put(`/oizap/perfis/${cd_perfil}`, dados);
            return response.data;
        } catch (error) {
            console.error('Erro ao atualizar perfil:', error);
            throw error;
        }
    },

    async deletarPerfil(cd_perfil) {
        try {
            const response = await api.delete(`/oizap/perfis/${cd_perfil}`, { data: { cd_perfil } });
            return response.data;
        } catch (error) {
            console.error('Erro ao deletar perfil:', error);
            throw error;
        }
    },

    // ===== TELAS =====
    async listarTelas(params = {}) {
        try {
            const response = await api.get('/oizap/telas', { params });
            return response.data;
        } catch (error) {
            console.error('Erro ao listar telas:', error);
            throw error;
        }
    },

    async buscarTelaPorId(cd_tela) {
        try {
            const response = await api.get(`/oizap/telas/${cd_tela}`);
            return response.data;
        } catch (error) {
            console.error('Erro ao buscar tela:', error);
            throw error;
        }
    },

    async criarTela(dados) {
        try {
            const response = await api.post('/oizap/telas', dados);
            return response.data;
        } catch (error) {
            console.error('Erro ao criar tela:', error);
            throw error;
        }
    },

    async atualizarTela(cd_tela, dados) {
        try {
            const response = await api.put(`/oizap/telas/${cd_tela}`, dados);
            return response.data;
        } catch (error) {
            console.error('Erro ao atualizar tela:', error);
            throw error;
        }
    },

    async deletarTela(cd_tela) {
        try {
            const response = await api.delete(`/oizap/telas/${cd_tela}`, { data: { cd_tela } });
            return response.data;
        } catch (error) {
            console.error('Erro ao deletar tela:', error);
            throw error;
        }
    },

    // ===== ASSOCIAÇÕES PERFIL-TELA =====
    async listarAssociacoesPerfisTelasPorPerfil(cd_perfil) {
        try {
            const response = await api.get('/oizap/perfis-telas', { params: { cd_perfil } });
            return response.data;
        } catch (error) {
            console.error('Erro ao listar associações perfil-tela:', error);
            throw error;
        }
    },

    async associarPerfilTela(dados) {
        try {
            const response = await api.post('/oizap/perfis-telas', dados);
            return response.data;
        } catch (error) {
            console.error('Erro ao associar perfil-tela:', error);
            throw error;
        }
    },

    async desassociarPerfilTela(cd_perfil, cd_tela) {
        try {
            const response = await api.delete('/oizap/perfis-telas', { 
                data: { cd_perfil, cd_tela } 
            });
            return response.data;
        } catch (error) {
            console.error('Erro ao desassociar perfil-tela:', error);
            throw error;
        }
    },

    async atualizarPermissoes(dados) {
        try {
            const response = await api.put('/oizap/perfis-telas/permissoes', dados);
            return response.data;
        } catch (error) {
            console.error('Erro ao atualizar permissões:', error);
            throw error;
        }
    },

    // ===== ASSOCIAÇÕES USUÁRIO-PERFIL =====
    async listarPerfisPorUsuario(cd_usuario) {
        try {
            const response = await api.get(`/oizap/usuarios/${cd_usuario}/perfis`);
            return response.data;
        } catch (error) {
            console.error('Erro ao listar perfis do usuário:', error);
            throw error;
        }
    },

    async associarUsuarioPerfil(dados) {
        try {
            const response = await api.post('/oizap/usuarios-perfis', dados);
            return response.data;
        } catch (error) {
            console.error('Erro ao associar usuário-perfil:', error);
            throw error;
        }
    },

    async desassociarUsuarioPerfil(cd_usuario, cd_perfil) {
        try {
            const response = await api.delete('/oizap/usuarios-perfis', { 
                data: { cd_usuario, cd_perfil } 
            });
            return response.data;
        } catch (error) {
            console.error('Erro ao desassociar usuário-perfil:', error);
            throw error;
        }
    },

    async substituirPerfisUsuario(cd_usuario, perfis) {
        try {
            const response = await api.put('/oizap/usuarios-perfis/substituir', { 
                cd_usuario, 
                perfis 
            });
            return response.data;
        } catch (error) {
            console.error('Erro ao substituir perfis do usuário:', error);
            throw error;
        }
    },

    // ===== VERIFICAÇÃO DE ACESSO =====
    async verificarAcesso(cd_usuario, ds_rota) {
        try {
            const response = await api.get(`/oizap/telas/acesso/${cd_usuario}/${encodeURIComponent(ds_rota)}`);
            return response.data;
        } catch (error) {
            console.error('Erro ao verificar acesso:', error);
            throw error;
        }
    },

    // ===== MENU BASEADO EM PERFIS =====
    async obterMenuPorUsuario(cd_usuario) {
        try {
            // Buscar perfis do usuário
            const perfisResponse = await this.listarPerfisPorUsuario(cd_usuario);
            
            if (perfisResponse.statuscode !== 200 || !perfisResponse.data.length) {
                return { menu: [] };
            }

            // Buscar telas associadas aos perfis
            const telasPermitidas = new Map();
            
            for (const perfil of perfisResponse.data) {
                const associacoesResponse = await this.listarAssociacoesPerfisTelasPorPerfil(perfil.cd_perfil);
                
                if (associacoesResponse.statuscode === 200) {
                    associacoesResponse.data.forEach(associacao => {
                        const key = associacao.ds_rota;
                        if (!telasPermitidas.has(key) || 
                            telasPermitidas.get(key).permissoes.in_visualizar < associacao.in_visualizar) {
                            telasPermitidas.set(key, {
                                tela: associacao,
                                permissoes: {
                                    in_visualizar: associacao.in_visualizar,
                                    in_inserir: associacao.in_inserir,
                                    in_alterar: associacao.in_alterar,
                                    in_excluir: associacao.in_excluir
                                }
                            });
                        }
                    });
                }
            }

            // Construir menu baseado nas telas permitidas
            const menu = this.construirMenuPorTelas(Array.from(telasPermitidas.values()));
            
            return { menu, telasPermitidas: Array.from(telasPermitidas.values()) };
        } catch (error) {
            console.error('Erro ao obter menu por usuário:', error);
            return { menu: [] };
        }
    },

    construirMenuPorTelas(telasPermitidas) {
        const menu = [];
        
        // Mapear telas para itens de menu
        const menuMap = {
            '/oizap/perfis': {
                icon: 'UsersIcon',
                pageName: 'gestao-perfis',
                title: 'Gestão de Perfis',
                desc: 'Gerencie perfis de acesso'
            },
            '/oizap/telas': {
                icon: 'LayoutIcon',
                pageName: 'gestao-telas', 
                title: 'Gestão de Telas',
                desc: 'Gerencie telas do sistema'
            },
            '/oizap/perfis-telas': {
                icon: 'LinkIcon',
                pageName: 'gestao-perfis-telas',
                title: 'Perfis & Telas',
                desc: 'Associe perfis às telas'
            },
            '/oizap/usuarios-perfis': {
                icon: 'UserCheckIcon',
                pageName: 'gestao-usuarios-perfis',
                title: 'Usuários & Perfis', 
                desc: 'Associe usuários aos perfis'
            }
        };

        // Verificar se há telas administrativas
        const telasAdmin = telasPermitidas.filter(item => 
            menuMap[item.tela.ds_rota] && item.permissoes.in_visualizar
        );

        if (telasAdmin.length > 0) {
            menu.push({
                icon: 'SettingsIcon',
                title: 'Administração',
                subMenu: telasAdmin.map(item => ({
                    ...menuMap[item.tela.ds_rota],
                    permissoes: item.permissoes
                }))
            });
        }

        return menu;
    }
};
