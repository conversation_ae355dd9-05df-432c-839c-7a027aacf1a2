import { Request } from 'express';
import { IRetorno, erroInterno, sucesso, conflito, dadosNaoEncontrados, parametrosInvalidos } from '../../interfaces/IRetorno';
import { PerfisTelaDB } from '../../data/perfis/PerfisTelaDB';
import { PerfisDB } from '../../data/perfis/PerfisDB';
import { TelasDB } from '../../data/telas/TelasDB';

export class PerfisTelaModel {
  async associar(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_perfil) {
        return parametrosInvalidos(['O campo "cd_perfil" é obrigatório']);
      }

      if (!req.body.cd_tela) {
        return parametrosInvalidos(['O campo "cd_tela" é obrigatório']);
      }

      // Verificar se o perfil existe
      req.params = { cd_perfil: req.body.cd_perfil };
      const perfilExistente = await PerfisDB.buscarPorId(req);
      
      if (perfilExistente.statuscode !== 200 || perfilExistente.data.length === 0) {
        return dadosNaoEncontrados('Perfil não encontrado');
      }

      // Verificar se a tela existe
      req.params = { cd_tela: req.body.cd_tela };
      const telaExistente = await TelasDB.buscarPorId(req);
      
      if (telaExistente.statuscode !== 200 || telaExistente.data.length === 0) {
        return dadosNaoEncontrados('Tela não encontrada');
      }

      // Verificar se a associação já existe
      const associacaoExistente = await PerfisTelaDB.verificarExistencia(req.body.cd_perfil, req.body.cd_tela);
      
      if (associacaoExistente.statuscode === 200 && associacaoExistente.data.length > 0) {
        return conflito('Esta associação já existe');
      }

      // Definir permissões padrão se não informadas
      if (req.body.in_visualizar === undefined) req.body.in_visualizar = true;
      if (req.body.in_inserir === undefined) req.body.in_inserir = false;
      if (req.body.in_alterar === undefined) req.body.in_alterar = false;
      if (req.body.in_excluir === undefined) req.body.in_excluir = false;

      // Criar a associação
      const resultado = await PerfisTelaDB.incluir(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Associação criada com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async desassociar(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_perfil) {
        return parametrosInvalidos(['O campo "cd_perfil" é obrigatório']);
      }

      if (!req.body.cd_tela) {
        return parametrosInvalidos(['O campo "cd_tela" é obrigatório']);
      }

      // Verificar se a associação existe
      const associacaoExistente = await PerfisTelaDB.verificarExistencia(req.body.cd_perfil, req.body.cd_tela);
      
      if (associacaoExistente.statuscode !== 200 || associacaoExistente.data.length === 0) {
        return dadosNaoEncontrados('Associação não encontrada');
      }

      // Remover a associação
      const resultado = await PerfisTelaDB.removerPorPerfilETela(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Associação removida com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async atualizarPermissoes(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_perfil) {
        return parametrosInvalidos(['O campo "cd_perfil" é obrigatório']);
      }

      if (!req.body.cd_tela) {
        return parametrosInvalidos(['O campo "cd_tela" é obrigatório']);
      }

      // Verificar se a associação existe
      const associacaoExistente = await PerfisTelaDB.verificarExistencia(req.body.cd_perfil, req.body.cd_tela);
      
      if (associacaoExistente.statuscode !== 200 || associacaoExistente.data.length === 0) {
        return dadosNaoEncontrados('Associação não encontrada');
      }

      // Validar permissões (pelo menos visualizar deve estar ativo)
      if (!req.body.in_visualizar && (req.body.in_inserir || req.body.in_alterar || req.body.in_excluir)) {
        return parametrosInvalidos(['Para ter permissões de inserir, alterar ou excluir, é necessário ter permissão de visualizar']);
      }

      // Atualizar as permissões
      const resultado = await PerfisTelaDB.atualizarPermissoes(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Permissões atualizadas com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listar(req: Request): Promise<IRetorno> {
    try {
      const resultado = await PerfisTelaDB.listar(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Associações listadas com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async buscarPorId(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.params.nr_perfil_tela) {
        return parametrosInvalidos(['O parâmetro "nr_perfil_tela" é obrigatório']);
      }

      const resultado = await PerfisTelaDB.buscarPorId(req);
      
      if (resultado.statuscode === 200) {
        if (resultado.data.length === 0) {
          return dadosNaoEncontrados('Associação não encontrada');
        }
        return sucesso('Associação encontrada', resultado.data[0]);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async associarMultiplas(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_perfil) {
        return parametrosInvalidos(['O campo "cd_perfil" é obrigatório']);
      }

      if (!req.body.telas || !Array.isArray(req.body.telas) || req.body.telas.length === 0) {
        return parametrosInvalidos(['O campo "telas" deve ser um array com pelo menos uma tela']);
      }

      // Verificar se o perfil existe
      req.params = { cd_perfil: req.body.cd_perfil };
      const perfilExistente = await PerfisDB.buscarPorId(req);
      
      if (perfilExistente.statuscode !== 200 || perfilExistente.data.length === 0) {
        return dadosNaoEncontrados('Perfil não encontrado');
      }

      const resultados = [];
      const erros = [];

      // Processar cada tela
      for (const telaConfig of req.body.telas) {
        try {
          // Verificar se a tela existe
          req.params = { cd_tela: telaConfig.cd_tela };
          const telaExistente = await TelasDB.buscarPorId(req);
          
          if (telaExistente.statuscode !== 200 || telaExistente.data.length === 0) {
            erros.push(`Tela ${telaConfig.cd_tela} não encontrada`);
            continue;
          }

          // Verificar se a associação já existe
          const associacaoExistente = await PerfisTelaDB.verificarExistencia(req.body.cd_perfil, telaConfig.cd_tela);
          
          if (associacaoExistente.statuscode === 200 && associacaoExistente.data.length > 0) {
            erros.push(`Associação com tela ${telaConfig.cd_tela} já existe`);
            continue;
          }

          // Criar a associação
          const dadosAssociacao = {
            cd_perfil: req.body.cd_perfil,
            cd_tela: telaConfig.cd_tela,
            in_visualizar: telaConfig.in_visualizar !== undefined ? telaConfig.in_visualizar : true,
            in_inserir: telaConfig.in_inserir !== undefined ? telaConfig.in_inserir : false,
            in_alterar: telaConfig.in_alterar !== undefined ? telaConfig.in_alterar : false,
            in_excluir: telaConfig.in_excluir !== undefined ? telaConfig.in_excluir : false
          };

          req.body = dadosAssociacao;
          const resultado = await PerfisTelaDB.incluir(req);
          
          if (resultado.statuscode === 200) {
            resultados.push(resultado.data[0]);
          } else {
            erros.push(`Erro ao associar tela ${telaConfig.cd_tela}: ${resultado.message}`);
          }
        } catch (error: any) {
          erros.push(`Erro ao processar tela ${telaConfig.cd_tela}: ${error.message}`);
        }
      }

      if (resultados.length === 0 && erros.length > 0) {
        return erroInterno({ message: erros.join('; ') });
      }

      const mensagem = `${resultados.length} associações criadas com sucesso` +
                      (erros.length > 0 ? `. Erros: ${erros.join('; ')}` : '');

      return sucesso({ associacoes_criadas: resultados, erros }, mensagem);
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
