<template>
  <div class="gestao-perfis-modulos">
    <!-- Header -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="fas fa-users-cog"></i>
        Gestão de Perfis e Módulos
      </h1>
      <p class="page-description">
        Associe perfis de usuários aos módulos do sistema com permissões específicas
      </p>
    </div>

    <!-- Seleção de Perfil -->
    <div class="perfil-selector">
      <div class="selector-header">
        <h3>
          <i class="fas fa-user"></i>
          Selecionar Perfil
        </h3>
      </div>
      <div class="selector-content">
        <select v-model="perfilSelecionado" class="perfil-select" @change="carregarPermissoesPerfil">
          <option value="">Selecione um perfil</option>
          <option 
            v-for="perfil in perfis" 
            :key="perfil.cd_perfil" 
            :value="perfil.cd_perfil"
          >
            {{ perfil.nm_perfil }}
          </option>
        </select>
        <div v-if="perfilAtual" class="perfil-info">
          <span class="perfil-nome">{{ perfilAtual.nm_perfil }}</span>
          <span class="perfil-descricao">{{ perfilAtual.ds_perfil }}</span>
        </div>
      </div>
    </div>

    <!-- Módulos e Permissões -->
    <div v-if="perfilSelecionado" class="modulos-container">
      <div class="modulos-header">
        <h3>
          <i class="fas fa-cubes"></i>
          Módulos e Permissões
        </h3>
        <div class="actions">
          <button @click="selecionarTodosModulos" class="btn btn-outline">
            <i class="fas fa-check-double"></i>
            Selecionar Todos
          </button>
          <button @click="desselecionarTodosModulos" class="btn btn-outline">
            <i class="fas fa-times"></i>
            Desselecionar Todos
          </button>
          <button @click="salvarPermissoes" class="btn btn-primary" :disabled="salvando">
            <i v-if="salvando" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-save"></i>
            {{ salvando ? 'Salvando...' : 'Salvar Permissões' }}
          </button>
        </div>
      </div>

      <div class="modulos-grid">
        <div 
          v-for="modulo in modulosComPermissoes" 
          :key="modulo.cd_modulo"
          class="modulo-card"
          :class="{ 'modulo-selecionado': modulo.selecionado }"
        >
          <div class="modulo-header">
            <div class="modulo-info">
              <label class="modulo-checkbox">
                <input
                  v-model="modulo.selecionado"
                  type="checkbox"
                  @change="toggleModulo(modulo)"
                >
                <span class="modulo-nome">{{ modulo.nm_modulo }}</span>
              </label>
              <p class="modulo-descricao">{{ modulo.ds_modulo }}</p>
            </div>
            <div class="modulo-stats">
              <span class="telas-count">{{ modulo.telas.length }} telas</span>
            </div>
          </div>

          <!-- Permissões CRUD para o módulo -->
          <div v-if="modulo.selecionado" class="permissoes-modulo">
            <h4>Permissões do Módulo</h4>
            <div class="permissoes-grid">
              <label class="permissao-item">
                <input
                  v-model="modulo.permissoes.in_visualizar"
                  type="checkbox"
                  @change="aplicarPermissaoTodasTelas(modulo, 'in_visualizar')"
                >
                <i class="fas fa-eye"></i>
                Visualizar
              </label>
              <label class="permissao-item">
                <input
                  v-model="modulo.permissoes.in_inserir"
                  type="checkbox"
                  @change="aplicarPermissaoTodasTelas(modulo, 'in_inserir')"
                >
                <i class="fas fa-plus"></i>
                Inserir
              </label>
              <label class="permissao-item">
                <input
                  v-model="modulo.permissoes.in_alterar"
                  type="checkbox"
                  @change="aplicarPermissaoTodasTelas(modulo, 'in_alterar')"
                >
                <i class="fas fa-edit"></i>
                Alterar
              </label>
              <label class="permissao-item">
                <input
                  v-model="modulo.permissoes.in_excluir"
                  type="checkbox"
                  @change="aplicarPermissaoTodasTelas(modulo, 'in_excluir')"
                >
                <i class="fas fa-trash"></i>
                Excluir
              </label>
            </div>
          </div>

          <!-- Telas do módulo -->
          <div v-if="modulo.selecionado && modulo.telas.length > 0" class="telas-container">
            <h4>
              <i class="fas fa-window-maximize"></i>
              Telas do Módulo
              <button 
                @click="modulo.telasExpanded = !modulo.telasExpanded"
                class="btn-toggle"
              >
                <i :class="modulo.telasExpanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
              </button>
            </h4>
            
            <div v-if="modulo.telasExpanded" class="telas-list">
              <div 
                v-for="tela in modulo.telas" 
                :key="tela.cd_tela"
                class="tela-item"
              >
                <div class="tela-info">
                  <label class="tela-checkbox">
                    <input
                      v-model="tela.selecionada"
                      type="checkbox"
                    >
                    <span class="tela-nome">{{ tela.nm_tela }}</span>
                  </label>
                  <span class="tela-rota">{{ tela.ds_rota }}</span>
                </div>
                
                <div v-if="tela.selecionada" class="tela-permissoes">
                  <label class="permissao-mini">
                    <input v-model="tela.permissoes.in_visualizar" type="checkbox">
                    <i class="fas fa-eye" title="Visualizar"></i>
                  </label>
                  <label class="permissao-mini">
                    <input v-model="tela.permissoes.in_inserir" type="checkbox">
                    <i class="fas fa-plus" title="Inserir"></i>
                  </label>
                  <label class="permissao-mini">
                    <input v-model="tela.permissoes.in_alterar" type="checkbox">
                    <i class="fas fa-edit" title="Alterar"></i>
                  </label>
                  <label class="permissao-mini">
                    <input v-model="tela.permissoes.in_excluir" type="checkbox">
                    <i class="fas fa-trash" title="Excluir"></i>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Estado vazio -->
    <div v-else class="empty-state">
      <i class="fas fa-user-plus"></i>
      <h3>Selecione um perfil</h3>
      <p>Escolha um perfil para gerenciar suas permissões de módulos e telas</p>
    </div>

    <!-- Loading -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Carregando...</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import AccessControlService from '@/services/administracao/AccessControlService'

const toast = useToast()

// Estados
const loading = ref(false)
const salvando = ref(false)
const perfilSelecionado = ref('')

// Dados
const perfis = ref([])
const modulosComPermissoes = ref([])

// Computed
const perfilAtual = computed(() => {
  return perfis.value.find(p => p.cd_perfil == perfilSelecionado.value)
})

// Métodos
const carregarDados = async () => {
  loading.value = true
  try {
    await Promise.all([
      carregarPerfis(),
      carregarModulosComTelas()
    ])
  } catch (error) {
    console.error('Erro ao carregar dados:', error)
    toast.error('Erro ao carregar dados')
  } finally {
    loading.value = false
  }
}

const carregarPerfis = async () => {
  try {
    const response = await AccessControlService.listarPerfis()
    if (response.statuscode === 200) {
      perfis.value = response.data || []
    }
  } catch (error) {
    console.error('Erro ao carregar perfis:', error)
    throw error
  }
}

const carregarModulosComTelas = async () => {
  try {
    const response = await AccessControlService.listarTelasAgrupadasPorModulo()
    if (response.statuscode === 200) {
      const modulos = response.data || []
      
      // Preparar estrutura com permissões
      modulosComPermissoes.value = modulos.map(modulo => ({
        ...modulo,
        selecionado: false,
        telasExpanded: false,
        permissoes: {
          in_visualizar: false,
          in_inserir: false,
          in_alterar: false,
          in_excluir: false
        },
        telas: modulo.telas.map(tela => ({
          ...tela,
          selecionada: false,
          permissoes: {
            in_visualizar: false,
            in_inserir: false,
            in_alterar: false,
            in_excluir: false
          }
        }))
      }))
    }
  } catch (error) {
    console.error('Erro ao carregar módulos:', error)
    throw error
  }
}

const carregarPermissoesPerfil = async () => {
  if (!perfilSelecionado.value) return
  
  try {
    // Carregar permissões existentes do perfil
    const response = await AccessControlService.listarPermissoesPerfil(perfilSelecionado.value)
    if (response.statuscode === 200) {
      const permissoes = response.data || []
      
      // Aplicar permissões existentes na estrutura
      modulosComPermissoes.value.forEach(modulo => {
        const telasDoModulo = modulo.telas
        let moduloTemPermissao = false
        
        telasDoModulo.forEach(tela => {
          const permissaoExistente = permissoes.find(p => p.cd_tela === tela.cd_tela)
          if (permissaoExistente) {
            tela.selecionada = true
            tela.permissoes = {
              in_visualizar: permissaoExistente.in_visualizar || false,
              in_inserir: permissaoExistente.in_inserir || false,
              in_alterar: permissaoExistente.in_alterar || false,
              in_excluir: permissaoExistente.in_excluir || false
            }
            moduloTemPermissao = true
          } else {
            tela.selecionada = false
            tela.permissoes = {
              in_visualizar: false,
              in_inserir: false,
              in_alterar: false,
              in_excluir: false
            }
          }
        })
        
        modulo.selecionado = moduloTemPermissao
        if (moduloTemPermissao) {
          // Calcular permissões do módulo baseado nas telas
          const telasComPermissao = telasDoModulo.filter(t => t.selecionada)
          modulo.permissoes = {
            in_visualizar: telasComPermissao.every(t => t.permissoes.in_visualizar),
            in_inserir: telasComPermissao.every(t => t.permissoes.in_inserir),
            in_alterar: telasComPermissao.every(t => t.permissoes.in_alterar),
            in_excluir: telasComPermissao.every(t => t.permissoes.in_excluir)
          }
        }
      })
    }
  } catch (error) {
    console.error('Erro ao carregar permissões do perfil:', error)
    toast.error('Erro ao carregar permissões do perfil')
  }
}

const toggleModulo = (modulo) => {
  if (modulo.selecionado) {
    // Selecionar todas as telas do módulo
    modulo.telas.forEach(tela => {
      tela.selecionada = true
      tela.permissoes = { ...modulo.permissoes }
    })
    modulo.telasExpanded = true
  } else {
    // Desselecionar todas as telas do módulo
    modulo.telas.forEach(tela => {
      tela.selecionada = false
      tela.permissoes = {
        in_visualizar: false,
        in_inserir: false,
        in_alterar: false,
        in_excluir: false
      }
    })
    modulo.permissoes = {
      in_visualizar: false,
      in_inserir: false,
      in_alterar: false,
      in_excluir: false
    }
  }
}

const aplicarPermissaoTodasTelas = (modulo, permissao) => {
  modulo.telas.forEach(tela => {
    if (tela.selecionada) {
      tela.permissoes[permissao] = modulo.permissoes[permissao]
    }
  })
}

const selecionarTodosModulos = () => {
  modulosComPermissoes.value.forEach(modulo => {
    modulo.selecionado = true
    modulo.permissoes = {
      in_visualizar: true,
      in_inserir: true,
      in_alterar: true,
      in_excluir: true
    }
    toggleModulo(modulo)
  })
}

const desselecionarTodosModulos = () => {
  modulosComPermissoes.value.forEach(modulo => {
    modulo.selecionado = false
    toggleModulo(modulo)
  })
}

const salvarPermissoes = async () => {
  if (!perfilSelecionado.value) {
    toast.error('Selecione um perfil')
    return
  }

  salvando.value = true
  try {
    // Coletar todas as permissões de telas selecionadas
    const permissoes = []

    modulosComPermissoes.value.forEach(modulo => {
      modulo.telas.forEach(tela => {
        if (tela.selecionada) {
          permissoes.push({
            cd_perfil: perfilSelecionado.value,
            cd_tela: tela.cd_tela,
            in_visualizar: tela.permissoes.in_visualizar,
            in_inserir: tela.permissoes.in_inserir,
            in_alterar: tela.permissoes.in_alterar,
            in_excluir: tela.permissoes.in_excluir
          })
        }
      })
    })

    // Salvar permissões
    const response = await AccessControlService.salvarPermissoesPerfil(perfilSelecionado.value, permissoes)

    if (response.statuscode === 200 || response.statuscode === 201) {
      toast.success('Permissões salvas com sucesso!')
    } else {
      toast.error(response.message || 'Erro ao salvar permissões')
    }
  } catch (error) {
    console.error('Erro ao salvar permissões:', error)
    toast.error('Erro ao salvar permissões')
  } finally {
    salvando.value = false
  }
}

// Lifecycle
onMounted(async () => {
  await carregarDados()
})
</script>

<style scoped>
.gestao-perfis-modulos {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
}

.perfil-selector {
  background: white;
  border: 1px solid #ecf0f1;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.selector-header h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.selector-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.perfil-select {
  padding: 12px 16px;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 16px;
  min-width: 250px;
  transition: border-color 0.3s ease;
}

.perfil-select:focus {
  outline: none;
  border-color: #3498db;
}

.perfil-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.perfil-nome {
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
}

.perfil-descricao {
  color: #7f8c8d;
  font-size: 14px;
}

.modulos-container {
  background: white;
  border: 1px solid #ecf0f1;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.modulos-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #ecf0f1;
}

.modulos-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
}

.actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-primary:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.btn-outline {
  background: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.modulos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.modulo-card {
  border: 2px solid #ecf0f1;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  background: #fafbfc;
}

.modulo-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.modulo-selecionado {
  border-color: #3498db;
  background: #f8f9fa;
}

.modulo-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.modulo-info {
  flex: 1;
}

.modulo-checkbox {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  margin-bottom: 8px;
}

.modulo-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.modulo-nome {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.modulo-descricao {
  color: #7f8c8d;
  margin: 0;
  line-height: 1.4;
}

.modulo-stats {
  display: flex;
  align-items: center;
}

.telas-count {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.permissoes-modulo {
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e8f4f8;
}

.permissoes-modulo h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.permissoes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.permissao-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.permissao-item:hover {
  background: #f8f9fa;
}

.permissao-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.permissao-item i {
  width: 16px;
  color: #7f8c8d;
}

.telas-container {
  border-top: 1px solid #ecf0f1;
  padding-top: 16px;
}

.telas-container h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.btn-toggle {
  background: none;
  border: none;
  color: #7f8c8d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-toggle:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

.tela-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border: 1px solid #ecf0f1;
  border-radius: 6px;
  margin-bottom: 8px;
}

.tela-info {
  flex: 1;
}

.tela-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  margin-bottom: 4px;
}

.tela-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.tela-nome {
  font-weight: 500;
  color: #2c3e50;
}

.tela-rota {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #e74c3c;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.tela-permissoes {
  display: flex;
  gap: 8px;
}

.permissao-mini {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 4px 6px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.permissao-mini:hover {
  background: #f8f9fa;
}

.permissao-mini input[type="checkbox"] {
  width: 14px;
  height: 14px;
}

.permissao-mini i {
  font-size: 12px;
  color: #7f8c8d;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
  color: #bdc3c7;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #2c3e50;
}

.empty-state p {
  margin: 0;
  font-size: 16px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  text-align: center;
  color: #3498db;
}

.loading-spinner i {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

@media (max-width: 768px) {
  .gestao-perfis-modulos {
    padding: 16px;
  }

  .selector-content {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .perfil-select {
    min-width: auto;
  }

  .modulos-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .modulos-grid {
    grid-template-columns: 1fr;
  }

  .permissoes-grid {
    grid-template-columns: 1fr;
  }

  .tela-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .tela-permissoes {
    justify-content: center;
  }
}
</style>
</script>
