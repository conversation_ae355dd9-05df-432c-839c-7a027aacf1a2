<template>
  <div>
    <h1>Teste de Requisição</h1>
    <button @click="testarRequisicao">Testar Requisição para /perfis</button>
    <div v-if="resultado">
      <h3>Resultado:</h3>
      <pre>{{ resultado }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const resultado = ref(null)

console.log('🔍 [TESTE] Script carregado')

onMounted(() => {
  console.log('🔍 [TESTE] onMounted executado')
})

async function testarRequisicao() {
  console.log('🔍 [TESTE] Botão clicado - fazendo requisição')
  
  try {
    const response = await fetch('http://localhost:3100/oizap/perfis', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    const data = await response.json()
    console.log('🔍 [TESTE] Resposta da API:', data)
    resultado.value = data
    
  } catch (error) {
    console.error('❌ [TESTE] Erro na requisição:', error)
    resultado.value = { erro: error.message }
  }
}
</script>
