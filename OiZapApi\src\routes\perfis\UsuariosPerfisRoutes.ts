import { Router } from 'express';
import authApi from '../../middleware/authApi';
import { UsuarioPerfilController } from '../../controllers/perfis/UsuarioPerfilController';

const router = Router();

// Rotas para gestão de associações usuário-perfil
router.post('/oizap/usuarios-perfis', authApi, UsuarioPerfilController.associarUsuarioPerfil);
router.delete('/oizap/usuarios-perfis', authApi, UsuarioPerfilController.desassociarUsuarioPerfil);
router.get('/oizap/usuarios-perfis', authApi, UsuarioPerfilController.listarAssociacoes);
router.get('/oizap/usuarios-perfis/:id', authApi, UsuarioPerfilController.buscarAssociacaoPorId);

// Rotas para listar perfis por usuário
router.get('/oizap/usuarios/:cd_usuario/perfis', authApi, UsuarioPerfilController.listarPerfisPorUsuario);

// Rota para listar usuários sem perfil
router.get('/oizap/usuarios-sem-perfil', authApi, UsuarioPerfilController.listarUsuariosSemPerfil);

// Rota para associar múltiplos perfis a um usuário
router.post('/oizap/usuarios-perfis/multiplos', authApi, UsuarioPerfilController.associarMultiplosPerfis);

// Rota para substituir todos os perfis de um usuário
router.put('/oizap/usuarios-perfis/substituir', authApi, UsuarioPerfilController.substituirPerfis);

export { router as UsuariosPerfisRoutes };
