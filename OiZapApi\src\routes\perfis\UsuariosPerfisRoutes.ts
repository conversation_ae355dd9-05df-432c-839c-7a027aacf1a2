import { Router } from 'express';
import authApi from '../../middleware/authApi';
import { UsuarioPerfilController } from '../../controllers/perfis/UsuarioPerfilController';

const router = Router();

console.log('🔧 [DEBUG] UsuariosPerfisRoutes - Arquivo carregado');

// IMPORTANTE: Rotas específicas devem vir ANTES das rotas genéricas

// Rota de teste simples
router.get('/usuarios/test', (req, res) => {
    console.log('🔍 [DEBUG] Rota de teste /usuarios/test funcionando');
    res.json({ message: 'Rota de teste funcionando', statuscode: 200 });
});

console.log('🔧 [DEBUG] UsuariosPerfisRoutes - Rota de teste registrada');

// Rotas para listar perfis por usuário (temporariamente sem auth para debug)
router.get('/usuarios/:cd_usuario/perfis', UsuarioPerfilController.listarPerfisPorUsuario);

console.log('🔧 [DEBUG] UsuariosPerfisRoutes - Rota de perfis registrada');

// Rotas para gestão de associações usuário-perfil
router.post('/usuarios-perfis', authApi, UsuarioPerfilController.associarUsuarioPerfil);
router.delete('/usuarios-perfis', authApi, UsuarioPerfilController.desassociarUsuarioPerfil);
router.get('/usuarios-perfis', authApi, UsuarioPerfilController.listarAssociacoes);
router.get('/usuarios-perfis/:id', authApi, UsuarioPerfilController.buscarAssociacaoPorId);

// Rota para listar usuários sem perfil
router.get('/usuarios-sem-perfil', authApi, UsuarioPerfilController.listarUsuariosSemPerfil);

// Rota para associar múltiplos perfis a um usuário
router.post('/usuarios-perfis/multiplos', authApi, UsuarioPerfilController.associarMultiplosPerfis);

// Rota para substituir todos os perfis de um usuário
router.put('/usuarios-perfis/substituir', authApi, UsuarioPerfilController.substituirPerfis);

console.log('🔧 [DEBUG] UsuariosPerfisRoutes - Exportando router com', router.stack.length, 'rotas');

export { router as UsuariosPerfisRoutes };
