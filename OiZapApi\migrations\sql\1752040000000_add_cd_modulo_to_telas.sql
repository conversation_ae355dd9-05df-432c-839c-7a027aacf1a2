-- Migration: add_cd_modulo_to_telas
-- Created: 2025-01-09T04:00:00.000Z
-- Environment: ALL

-- ========================================
-- UP: Adicionar campo cd_modulo na tabela telas
-- ========================================

-- Adicionar coluna cd_modulo na tabela telas
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'telas' 
        AND column_name = 'cd_modulo'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE telas ADD COLUMN cd_modulo INTEGER;
        COMMENT ON COLUMN telas.cd_modulo IS 'Código do módulo ao qual a tela pertence';
        RAISE NOTICE 'Coluna cd_modulo adicionada à tabela telas';
    ELSE
        RAISE NOTICE 'Coluna cd_modulo já existe na tabela telas';
    END IF;
END $$;

-- Criar índice para otimizar consultas por módulo
CREATE INDEX IF NOT EXISTS idx_telas_cd_modulo ON telas(cd_modulo);
COMMENT ON INDEX idx_telas_cd_modulo IS 'Índice para busca de telas por módulo';

-- Criar chave estrangeira para modulos (se a tabela modulos existir)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'modulos') THEN
        -- Verificar se a constraint já existe
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_telas_modulo' 
            AND table_name = 'telas'
        ) THEN
            ALTER TABLE telas 
            ADD CONSTRAINT fk_telas_modulo 
            FOREIGN KEY (cd_modulo) 
            REFERENCES modulos(cd_modulo) 
            ON DELETE SET NULL;
            RAISE NOTICE 'Chave estrangeira fk_telas_modulo criada';
        ELSE
            RAISE NOTICE 'Chave estrangeira fk_telas_modulo já existe';
        END IF;
    ELSE
        RAISE NOTICE 'Tabela modulos não encontrada, chave estrangeira não criada';
    END IF;
END $$;

-- ========================================
-- DOWN: Remover alterações (para rollback)
-- ========================================

-- Para fazer rollback desta migration:
-- ALTER TABLE telas DROP CONSTRAINT IF EXISTS fk_telas_modulo;
-- DROP INDEX IF EXISTS idx_telas_cd_modulo;
-- ALTER TABLE telas DROP COLUMN IF EXISTS cd_modulo;
