import { Request } from 'express';
import { IRetorno, erroInterno, sucesso, conflito, dadosNaoEncontrados, parametrosInvalidos } from '../../interfaces/IRetorno';
import { TelasDB } from '../../data/telas/TelasDB';

export class TelasModel {
  async incluir(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.nm_tela) {
        return parametrosInvalidos(['O campo "nm_tela" é obrigatório']);
      }

      if (!req.body.ds_rota) {
        return parametrosInvalidos(['O campo "ds_rota" é obrigatório']);
      }

      if (req.body.nm_tela.length < 3) {
        return parametrosInvalidos(['O nome da tela deve ter pelo menos 3 caracteres']);
      }

      if (!req.body.ds_rota.startsWith('/')) {
        return parametrosInvalidos(['A rota deve começar com "/"']);
      }

      // Verificar se já existe uma tela com a mesma rota
      req.query = { ds_rota: req.body.ds_rota };
      const telaExistente = await TelasDB.listar(req);
      
      if (telaExistente.statuscode === 200 && telaExistente.data.length > 0) {
        return conflito('Já existe uma tela com esta rota');
      }

      // Definir valores padrão
      if (req.body.in_ativa === undefined) {
        req.body.in_ativa = true;
      }

      // Incluir a tela
      const resultado = await TelasDB.incluir(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Tela criada com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async alterar(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_tela) {
        return parametrosInvalidos(['O campo "cd_tela" é obrigatório']);
      }

      if (!req.body.nm_tela) {
        return parametrosInvalidos(['O campo "nm_tela" é obrigatório']);
      }

      if (!req.body.ds_rota) {
        return parametrosInvalidos(['O campo "ds_rota" é obrigatório']);
      }

      if (req.body.nm_tela.length < 3) {
        return parametrosInvalidos(['O nome da tela deve ter pelo menos 3 caracteres']);
      }

      if (!req.body.ds_rota.startsWith('/')) {
        return parametrosInvalidos(['A rota deve começar com "/"']);
      }

      // Verificar se a tela existe
      req.params = { cd_tela: req.body.cd_tela };
      const telaExistente = await TelasDB.buscarPorId(req);
      
      if (telaExistente.statuscode !== 200 || telaExistente.data.length === 0) {
        return dadosNaoEncontrados('Tela não encontrada');
      }

      // Verificar se já existe outra tela com a mesma rota
      req.query = { ds_rota: req.body.ds_rota };
      const outraTelaComMesmaRota = await TelasDB.listar(req);
      
      if (outraTelaComMesmaRota.statuscode === 200 && outraTelaComMesmaRota.data.length > 0) {
        const telaEncontrada = outraTelaComMesmaRota.data[0];
        if (telaEncontrada.cd_tela !== parseInt(req.body.cd_tela)) {
          return conflito('Já existe outra tela com esta rota');
        }
      }

      // Atualizar a tela
      const resultado = await TelasDB.alterar(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Tela atualizada com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async remover(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_tela) {
        return parametrosInvalidos(['O campo "cd_tela" é obrigatório']);
      }

      // Verificar se a tela existe
      req.params = { cd_tela: req.body.cd_tela };
      const telaExistente = await TelasDB.buscarPorId(req);
      
      if (telaExistente.statuscode !== 200 || telaExistente.data.length === 0) {
        return dadosNaoEncontrados('Tela não encontrada');
      }

      // Verificar se há perfis associados à tela
      const perfisAssociados = await TelasDB.listarPerfisPorTela(req);
      
      if (perfisAssociados.statuscode === 200 && perfisAssociados.data.length > 0) {
        return conflito('Não é possível remover a tela pois há perfis associados a ela');
      }

      // Remover a tela
      const resultado = await TelasDB.remover(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Tela removida com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listar(req: Request): Promise<IRetorno> {
    try {
      const resultado = await TelasDB.listar(req);

      if (resultado.statuscode === 200) {
        return sucesso('Telas listadas com sucesso', resultado.data);
      }

      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listarAgrupadasPorModulo(req: Request): Promise<IRetorno> {
    try {
      const resultado = await TelasDB.listarAgrupadasPorModulo(req);

      if (resultado.statuscode === 200) {
        return sucesso('Telas agrupadas por módulo listadas com sucesso', resultado.data);
      }

      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async buscarPorId(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.params.cd_tela) {
        return parametrosInvalidos(['O parâmetro "cd_tela" é obrigatório']);
      }

      const resultado = await TelasDB.buscarPorId(req);
      
      if (resultado.statuscode === 200) {
        if (resultado.data.length === 0) {
          return dadosNaoEncontrados('Tela não encontrada');
        }
        return sucesso('Tela encontrada', resultado.data[0]);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async listarPerfis(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.params.cd_tela) {
        return parametrosInvalidos(['O parâmetro "cd_tela" é obrigatório']);
      }

      const resultado = await TelasDB.listarPerfisPorTela(req);
      
      if (resultado.statuscode === 200) {
        return sucesso('Perfis da tela listados com sucesso', resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async verificarAcesso(cd_usuario: number, ds_rota: string): Promise<IRetorno> {
    try {
      const resultado = await TelasDB.verificarAcesso(cd_usuario, ds_rota);
      
      if (resultado.statuscode === 200) {
        if (resultado.data.length === 0) {
          return dadosNaoEncontrados('Usuário não possui acesso a esta tela');
        }
        return sucesso('Acesso verificado', resultado.data[0]);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  async ativarDesativar(req: Request): Promise<IRetorno> {
    try {
      // Validações
      if (!req.body.cd_tela) {
        return parametrosInvalidos(['O campo "cd_tela" é obrigatório']);
      }

      if (req.body.in_ativa === undefined) {
        return parametrosInvalidos(['O campo "in_ativa" é obrigatório']);
      }

      // Verificar se a tela existe
      req.params = { cd_tela: req.body.cd_tela };
      const telaExistente = await TelasDB.buscarPorId(req);
      
      if (telaExistente.statuscode !== 200 || telaExistente.data.length === 0) {
        return dadosNaoEncontrados('Tela não encontrada');
      }

      // Atualizar apenas o status
      const dadosAtualizacao = {
        cd_tela: req.body.cd_tela,
        in_ativa: req.body.in_ativa
      };
      
      req.body = dadosAtualizacao;
      const resultado = await TelasDB.alterar(req);
      
      if (resultado.statuscode === 200) {
        const status = req.body.in_ativa ? 'ativada' : 'desativada';
        return sucesso(`Tela ${status} com sucesso`, resultado.data);
      }
      
      return resultado;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
