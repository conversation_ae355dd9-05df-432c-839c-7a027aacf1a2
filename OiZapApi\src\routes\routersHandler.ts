// src/routes/suaRotaHandler.ts
import { Router } from 'express';

import { RegrasGlobais } from '../middleware/RegrasGlobais';
import { AdminRoutes } from '../routes/AdminRoutes';
import { AtualizacaoTabelasPDVRoutes } from '../routes/AtualizacaoTabelasPDVRoutes';
import { ClientesRoutes } from '../routes/ClientesRoutes';
import { ItemAdicionalRoutes } from '../routes/ItemAdicinoalRoutes';
import { MessagesRoutes } from '../routes/MessagesRoutes';
import { ModulosRoutes } from '../routes/ModulosRoutes';
import { TokenRoutes } from '../routes/TokenRoutes';
import { UploadRoutes } from '../routes/UploadRoutes';
import { AtendimentoHistoricosRoutes } from '../routes/atendimentos/AtendimentoHistoricosRoutes';
import { AtendimentosRoutes } from '../routes/atendimentos/AtendimentosRoutes';
import { EstabelecimentoModulosRoutes } from '../routes/estabelecimento/EstabelecimentoModulosRoutes';
import { EstabelecimentoRoutes } from '../routes/estabelecimento/EstabelecimentoRoutes';
import { FluxoAtendimentoRoutes } from '../routes/fluxos/FluxoAtendimentoRoutes';
import { FluxoConexaoRoutes } from '../routes/fluxos/FluxoConexaoRoutes';
import { FluxoEnderecoRoutes } from '../routes/fluxos/FluxoEnderecoRoutes';
import { FluxoEtapasRoutes } from '../routes/fluxos/FluxoEtapasRoutes';
import { FluxoImagemRoutes } from '../routes/fluxos/FluxoImagemRoutes';
import { FluxoOpcoesRoutes } from '../routes/fluxos/FluxoOpcoesRoutes';
import { FluxoTokensRoutes } from '../routes/fluxos/FluxoTokensRoutes';
import { FluxosRoutes } from '../routes/fluxos/FluxosRoutes';
import { MensagemRelacionadaRoutes } from '../routes/fluxos/MesangemRelacionadaRoutes';
import { PedidoItemAdicionalRoutes } from '../routes/pedidos/PedidoItemAdicionalRoutes';
import { PedidoItemRoutes } from '../routes/pedidos/PedidoItemRoutes';
import { PedidosRoutes } from '../routes/pedidos/PedidosRoutes';
import { AdicionaisRoutes } from '../routes/produtos/AdicionaisRoutes';
import { AdicionalAgrupadoRoutes } from '../routes/produtos/AdicionalAgrupadoRoutes';
import { GruposRoutes } from '../routes/produtos/GruposRoutes';
import { ProdutosRoutes } from '../routes/produtos/ProdutosRoutes';
import { SubGruposRoutes } from '../routes/produtos/SubGruposRoutes';
import { TamanhosRoutes } from '../routes/produtos/TamanhosRoutes';
import { UsuarioAcessosRoutes } from '../routes/usuarios/UsuarioAcessosRoutes';
import { UsuarioEstabelecimentosRoutes } from '../routes/usuarios/UsuarioEstabelecimentosRoutes';
import { UsuarioInstanciasRoutes } from '../routes/usuarios/UsuarioInstanciasRoutes';
import { UsuarioRoutes } from '../routes/usuarios/UsuarioRoutes';
import { ContatosRoutes } from './ContatosRoutes';
import { DockerApiEvolutionRoutes } from './DockerApiEvolutionRoutes';
import { EnderecoRoutes } from './EnderecosRoutes';
import { ExemplosRoutes } from './apiDocs/ExemplosRoutes';
import { CampanhasRoutes } from './campanhas/CampanhasRoutes';
import { DisparosPixelRoutes } from './campanhas/DisparosPixelRoutes';
import { DepartamentosRoutes } from './departamentos/DepartamentosRoutes';
import { EstabelecimentoInstanciasRoutes } from './estabelecimento/EstabelecimentoInstanciasRoutes';
import { MensagemHorariosRoutes } from './fluxos/MensagemHorariosRoutes';
import { InstanciaModulosRoutes } from './instancia/InstanciaModulosRoutes';
import { InstanciaRoutes } from './instancia/InstanciaRoutes';
import { MotivosAtendimentoRoutes } from './motivos-atendimento/MotivosAtendimentoRoutes';
import { ParametrosRoutes } from './parametros/ParametrosRoutes';
import { PedidoItemBordasRoutes } from './pedidos/PedidoItemBordasRoutes';
import { PedidoItemPizzaRoutes } from './pedidos/PedidoItemPizzaRoutes';
import { ProxyRoutes } from './proxy/ProxyRoutes';
import { VersaoRoutes } from './versao/VersaoRoutes';

const router = Router();

const rotas = [
  EstabelecimentoRoutes,
  UsuarioRoutes,
  ClientesRoutes,
  ProdutosRoutes,
  AdicionaisRoutes,
  AdicionalAgrupadoRoutes,
  AtualizacaoTabelasPDVRoutes,
  GruposRoutes,
  SubGruposRoutes,
  TamanhosRoutes,
  AdminRoutes,
  ModulosRoutes,
  EstabelecimentoModulosRoutes,
  PedidosRoutes,
  PedidoItemRoutes,
  ItemAdicionalRoutes,
  MessagesRoutes,
  InstanciaRoutes,
  InstanciaModulosRoutes,
  UsuarioInstanciasRoutes,
  UsuarioAcessosRoutes,
  FluxoAtendimentoRoutes,
  FluxoConexaoRoutes,
  FluxoEnderecoRoutes,
  FluxoEtapasRoutes,
  FluxoImagemRoutes,
  FluxoOpcoesRoutes,
  FluxosRoutes,
  UsuarioEstabelecimentosRoutes,
  PedidoItemAdicionalRoutes,
  TokenRoutes,
  AtendimentosRoutes,
  UploadRoutes,
  FluxoTokensRoutes,
  MensagemRelacionadaRoutes,
  AtendimentoHistoricosRoutes,
  VersaoRoutes,
  DockerApiEvolutionRoutes,
  ProxyRoutes,
  PedidoItemPizzaRoutes,
  PedidoItemBordasRoutes,
  EnderecoRoutes,
  EstabelecimentoInstanciasRoutes,
  ExemplosRoutes,
  MensagemHorariosRoutes,
  ParametrosRoutes,
  CampanhasRoutes,
  DepartamentosRoutes,
  ContatosRoutes,
  MotivosAtendimentoRoutes,
  DisparosPixelRoutes,
];

rotas.forEach((route) => {
  router.use(RegrasGlobais, route);
});

export default router;
