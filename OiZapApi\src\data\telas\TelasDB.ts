require('dotenv').config();
import { Request } from 'express';
import { IRetorno, erroInterno } from '../../interfaces/IRetorno';
import { PostgreSQLServices, OperationObject } from '../../services/PostgreSQLServices';

export class TelasDB {
  static async incluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['telas'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async alterar(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['telas'],
        chaves: { cd_tela: req.body.cd_tela },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async remover(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['telas'],
        chaves: { cd_tela: req.body.cd_tela },
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listar(req: Request): Promise<IRetorno> {
    try {
      let sql = `
        SELECT 
          t.cd_tela,
          t.nm_tela,
          t.ds_rota,
          t.created_at,
     
          COUNT(pt.cd_perfil) as total_perfis
        FROM telas t
        LEFT JOIN perfis_telas pt ON t.cd_tela = pt.cd_tela
        WHERE 1=1
      `;

      if (req.query.cd_tela) {
        sql += ` AND t.cd_tela = '${req.query.cd_tela}'`;
      }
      if (req.query.nm_tela) {
        sql += ` AND LOWER(t.nm_tela) LIKE LOWER('%${req.query.nm_tela}%')`;
      }
      if (req.query.ds_rota) {
        sql += ` AND LOWER(t.ds_rota) LIKE LOWER('%${req.query.ds_rota}%')`;
      }
      if (req.query.in_ativa !== undefined) {
        sql += ` AND t.in_ativa = ${req.query.in_ativa}`;
      }

      sql += ` 
        GROUP BY t.cd_tela, t.nm_tela, t.ds_rota, t.created_at
        ORDER BY t.nm_tela
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async buscarPorId(req: Request): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          t.cd_tela,
          t.nm_tela,
          t.ds_rota,
          t.ds_descricao,
          t.in_ativa,
          t.created_at,
          t.updated_at
        FROM telas t
        WHERE t.cd_tela = '${req.params.cd_tela}'
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listarPerfisPorTela(req: Request): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          p.cd_perfil,
          p.nm_perfil,
          pt.in_visualizar,
          pt.in_inserir,
          pt.in_alterar,
          pt.in_excluir,
          pt.created_at as data_associacao
        FROM perfis_telas pt
        JOIN perfis p ON pt.cd_perfil = p.cd_perfil
        WHERE pt.cd_tela = '${req.params.cd_tela}'
        ORDER BY p.nm_perfil
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async verificarAcesso(cd_usuario: number, ds_rota: string): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          t.cd_tela,
          t.nm_tela,
          t.ds_rota,
          pt.in_visualizar,
          pt.in_inserir,
          pt.in_alterar,
          pt.in_excluir,
          p.nm_perfil
        FROM usuarios_perfis up
        JOIN perfis p ON up.cd_perfil = p.cd_perfil
        JOIN perfis_telas pt ON p.cd_perfil = pt.cd_perfil
        JOIN telas t ON pt.cd_tela = t.cd_tela
        WHERE up.cd_usuario = '${cd_usuario}'
          AND t.ds_rota = '${ds_rota}'
          AND t.in_ativa = true
        LIMIT 1
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
