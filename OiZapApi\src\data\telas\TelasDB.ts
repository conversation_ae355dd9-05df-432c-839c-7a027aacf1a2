require('dotenv').config();
import { Request } from 'express';
import { IRetorno, erroInterno } from '../../interfaces/IRetorno';
import { PostgreSQLServices, OperationObject } from '../../services/PostgreSQLServices';

export class TelasDB {
  static async incluir(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'insert',
        tabelas: ['telas'],
        chaves: undefined,
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async alterar(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'update',
        tabelas: ['telas'],
        chaves: { cd_tela: req.body.cd_tela },
        dados: req.body,
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async remover(req: Request): Promise<IRetorno> {
    try {
      const opDb: OperationObject = {
        operacao: 'delete',
        tabelas: ['telas'],
        chaves: { cd_tela: req.body.cd_tela },
        retorno: '*',
      };
      return await new PostgreSQLServices().executar(opDb);
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listar(req: Request): Promise<IRetorno> {
    try {
      let sql = `
        SELECT 
          t.cd_tela,
          t.nm_tela,
          t.ds_rota,
          t.created_at,
     
          COUNT(pt.cd_perfil) as total_perfis
        FROM telas t
        LEFT JOIN perfis_telas pt ON t.cd_tela = pt.cd_tela
        WHERE 1=1
      `;

      if (req.query.cd_tela) {
        sql += ` AND t.cd_tela = '${req.query.cd_tela}'`;
      }
      if (req.query.nm_tela) {
        sql += ` AND LOWER(t.nm_tela) LIKE LOWER('%${req.query.nm_tela}%')`;
      }
      if (req.query.ds_rota) {
        sql += ` AND LOWER(t.ds_rota) LIKE LOWER('%${req.query.ds_rota}%')`;
      }
      if (req.query.in_ativa !== undefined) {
        sql += ` AND t.in_ativa = ${req.query.in_ativa}`;
      }

      sql += ` 
        GROUP BY t.cd_tela, t.nm_tela, t.ds_rota, t.created_at
        ORDER BY t.nm_tela
      `;

      const result = await new PostgreSQLServices().query(sql);
      console.log('🔍 [DEBUG] TelasDB.listar - Resultado da query:', result);

      // Verificar se o resultado está correto
      if (result.statuscode === 200) {
        return {
          statuscode: 200,
          message: 'Telas listadas com sucesso',
          data: result.data
        };
      }

      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listarAgrupadasPorModulo(req: Request): Promise<IRetorno> {
    try {
      let sql = `
        SELECT
          m.cd_modulo,
          m.nm_modulo,
          m.ds_funcionalidade as ds_modulo,
          COALESCE(
            JSON_AGG(
              CASE
                WHEN t.cd_tela IS NOT NULL THEN
                  JSON_BUILD_OBJECT(
                    'cd_tela', t.cd_tela,
                    'nm_tela', t.nm_tela,
                    'ds_rota', t.ds_rota,
                    'ds_descricao', t.ds_descricao,
                    'in_ativa', t.in_ativa,
                    'created_at', t.created_at,
                    'total_perfis', COALESCE(perfil_count.total_perfis, 0)
                  )
                ELSE NULL
              END
            ) FILTER (WHERE t.cd_tela IS NOT NULL),
            '[]'::json
          ) as telas
        FROM modulos m
        LEFT JOIN telas t ON m.cd_modulo = t.cd_modulo AND (t.in_ativa = true OR t.in_ativa IS NULL)
        LEFT JOIN (
          SELECT
            pt.cd_tela,
            COUNT(pt.cd_perfil) as total_perfis
          FROM perfis_telas pt
          GROUP BY pt.cd_tela
        ) perfil_count ON t.cd_tela = perfil_count.cd_tela
        WHERE 1=1
      `;

      // Filtros opcionais
      if (req.query.cd_modulo) {
        sql += ` AND m.cd_modulo = '${req.query.cd_modulo}'`;
      }
      if (req.query.nm_modulo) {
        sql += ` AND LOWER(m.nm_modulo) LIKE LOWER('%${req.query.nm_modulo}%')`;
      }

      sql += `
        GROUP BY m.cd_modulo, m.nm_modulo, m.ds_funcionalidade
        ORDER BY m.nm_modulo
      `;

      const result = await new PostgreSQLServices().query(sql);
      console.log('🔍 [DEBUG] TelasDB.listarAgrupadasPorModulo - Resultado da query:', result);

      if (result.statuscode === 200) {
        // Processar os dados para garantir que telas seja sempre um array
        const processedData = result.data.map((modulo: any) => ({
          ...modulo,
          telas: Array.isArray(modulo.telas) ? modulo.telas : []
        }));

        return {
          statuscode: 200,
          message: 'Telas agrupadas por módulo listadas com sucesso',
          data: processedData
        };
      }

      return result;
    } catch (error: any) {
      console.error('🔍 [DEBUG] TelasDB.listarAgrupadasPorModulo - Erro:', error);
      return erroInterno(error);
    }
  }

  static async buscarPorId(req: Request): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          t.cd_tela,
          t.nm_tela,
          t.ds_rota,
          t.ds_descricao,
          t.in_ativa,
          t.created_at,
          t.updated_at
        FROM telas t
        WHERE t.cd_tela = '${req.params.cd_tela}'
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async listarPerfisPorTela(req: Request): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          p.cd_perfil,
          p.nm_perfil,
          pt.in_visualizar,
          pt.in_inserir,
          pt.in_alterar,
          pt.in_excluir,
          pt.created_at as data_associacao
        FROM perfis_telas pt
        JOIN perfis p ON pt.cd_perfil = p.cd_perfil
        WHERE pt.cd_tela = '${req.params.cd_tela}'
        ORDER BY p.nm_perfil
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }

  static async verificarAcesso(cd_usuario: number, ds_rota: string): Promise<IRetorno> {
    try {
      const sql = `
        SELECT 
          t.cd_tela,
          t.nm_tela,
          t.ds_rota,
          pt.in_visualizar,
          pt.in_inserir,
          pt.in_alterar,
          pt.in_excluir,
          p.nm_perfil
        FROM usuarios_perfis up
        JOIN perfis p ON up.cd_perfil = p.cd_perfil
        JOIN perfis_telas pt ON p.cd_perfil = pt.cd_perfil
        JOIN telas t ON pt.cd_tela = t.cd_tela
        WHERE up.cd_usuario = '${cd_usuario}'
          AND t.ds_rota = '${ds_rota}'
          AND t.in_ativa = true
        LIMIT 1
      `;

      const result = await new PostgreSQLServices().query(sql);
      return result;
    } catch (error: any) {
      return erroInterno(error);
    }
  }
}
