import cors from 'cors';
import dotenv from 'dotenv';
import express, { Application, NextFunction, Request, Response } from 'express';
import fs from 'fs';
import knex from 'knex';
import cron from 'node-cron';
import path from 'path';
import config from './config/knexfile';
import { NOT_FOUND } from './interfaces/IRetorno';
import LoggerMiddleware from './middleware/LoggerMiddleware';
import errorHandler from './middleware/errorHandler';
import { AtendimentosModel } from './models/atendimentos/AtendimentosModel';
import { InstanciaModel } from './models/instancia/InstanciaModel';
import routersHandler from './routes/routersHandler';
import Logger from './services/Logger';
dotenv.config();

const logger = Logger.getLogger();

export const app: Application = express();

app.use(LoggerMiddleware.getCustomMorganMiddleware());
app.use(express.json({ limit: '500mb' }));
app.use(express.urlencoded({ extended: true, limit: '500mb' }));
app.use(cors());

app.get('/oizap', (req: Request, res: Response, next: NextFunction) => {
  return res.send(`<h1>Oi Zap Api ${process.env.AMBIENTE}</h1>`);
});

const rotaPadrao: string = '/oizap';
const midiaPath = path.join(__dirname, 'midia');
const uploadsPath = path.join(__dirname, 'uploads');

// app.use(express.static(path.join(__dirname, 'public')));
// app.use(rotaPadrao + '/uploads', express.static(path.join(__dirname, 'uploads')));
// app.use(rotaPadrao + '/midia', express.static(path.join(__dirname, 'midia')));

// Use esta configuração
app.use(
  '/uploads',
  express.static(uploadsPath, {
    setHeaders: (res, path) => {
      // Força download para PDFs
      if (path.endsWith('.pdf')) {
        res.set('Content-Type', 'application/pdf');
      }
    },
  }),
);

app.use(
  `${rotaPadrao}/uploads`,
  express.static(uploadsPath, {
    setHeaders: (res, path) => {
      if (path.endsWith('.pdf')) {
        res.set('Content-Type', 'application/pdf');
      }
    },
  }),
);

// Use esta configuração
app.use(
  '/midia',
  express.static(midiaPath, {
    setHeaders: (res, path) => {
      // Força download para PDFs
      if (path.endsWith('.pdf')) {
        res.set('Content-Type', 'application/pdf');
      }
    },
  }),
);

app.use(
  `${rotaPadrao}/midia`,
  express.static(midiaPath, {
    setHeaders: (res, path) => {
      if (path.endsWith('.pdf')) {
        res.set('Content-Type', 'application/pdf');
      }
    },
  }),
);
// Debug: Log das rotas registradas
console.log('🔧 [DEBUG] Registrando rotas no caminho:', rotaPadrao);
console.log('🔧 [DEBUG] RouterHandler carregado com sucesso');

app.use(rotaPadrao, routersHandler);

// Middleware de erro deve vir por último
app.use(errorHandler);

app.use((req: Request, res: Response, next: NextFunction) => {
  return res.status(NOT_FOUND).send({ statuscode: NOT_FOUND, message: 'Endpoint não encontrado', data: [] });
});

function iniciarProcessoCron() {
  //console.log('Processo de atualização com cron iniciado...');
  // Agendado para rodar todo início de hora
  // cron.schedule('0 * * * *', async () => {
  cron.schedule('*/10 * * * *', async () => {
    logger.info('Processo de encerraAtendimentoAberto com cron iniciado...');
    await new AtendimentosModel().encerraAtendimentoAberto();
    logger.info('Processo de encerraAtendimentoAberto com cron finalizado!');
  });

  // cron.schedule('0 4 * * *', async () => {
  //   //cron.schedule('* * * * *', async () => {
  //   let appName = '';
  //   if (process.env.AMBIENTE == 'PROD') {
  //     appName = process.env.PM2_CONSUMER_APPNAME || '';
  //   } else if (process.env.AMBIENTE == 'SANDBOX') {
  //     appName = process.env.PM2_CONSUMER_APPNAMESANDBOX || '';
  //   } else {
  //     appName = process.env.PM2_CONSUMER_APPNAMEDEV || '';
  //   }
  //   logger.info('Parando ' + appName);

  //   let resp = await FTPService.stopPM2(appName);
  //   logger.info('Retorno do evolution_v2:' + resp.message);

  //   logger.info('Reiniciando o rabbitmq...');
  //   let resp2 = await PortainerServices.restartStack('rabbitmq');
  //   logger.info('Retorno do rabbitmq:' + resp2?.data);

  //   await new Promise((resolve) => setTimeout(resolve, 120000));

  //   logger.info('Reiniciando o 	postgres...');
  //   resp2 = await PortainerServices.restartStack('postgres');
  //   logger.info('Retorno do postgres:' + resp2?.data);

  //   await new Promise((resolve) => setTimeout(resolve, 120000));
  //   logger.info('Reiniciando o 	evolution_v2...');
  //   resp2 = await PortainerServices.restartStack('evolution_v2');
  //   logger.info('Retorno do evolution_v2:' + resp2.message);

  //   await new Promise((resolve) => setTimeout(resolve, 120000));

  //   logger.info('Reiniciando ' + appName);

  //   await FTPService.restartPM2(appName);

  //   logger.info(appName + ' Reiniciado com sucesso!');
  // });
}

async function restart() {
  await new InstanciaModel().restartInstanciaInterno();
}

//RESTART ESTÁ PRONTO, AGORA SÓ TEM QUE VER A CONFUSÃO DO HASH DO ESTABELECIMENTO QEU ESTA PEGANDO
//DA TABELA DE RELACIONAMENTO DE INSTANCIA COM O ESTABELECIMENTO, PORÉM O CORRETO É DA TABELA DE ESTABELECIMENTO
//SERÁ ?????

//restart();

const startMigrations = async () => {
  try {
    // if (process.env.AMBIENTE === 'DEV') {
    //   logger.info('Ambiente DEV: Pulando verificação de migrations');
    //   return;
    // }

    const migrationsPath = path.join(__dirname, '..', 'database', 'migrations');
    if (!fs.existsSync(migrationsPath)) {
      fs.mkdirSync(migrationsPath, { recursive: true });
      logger.info(`Diretório de migrations criado: ${migrationsPath}`);
    }

    try {
      const knexInstance = knex(config);

      // 🔧 Auto-limpeza de migrations órfãs
      // await cleanOrphanMigrations(knexInstance, migrationsPath);

      // Executa as migrations
      await knexInstance.migrate.latest();
      logger.info('Migrations executadas com sucesso!');
    } catch (migrationError: any) {
      logger.error('Erro ao executar migrations:', migrationError?.message || migrationError);
      logger.warn('API continuará executando mesmo com erro nas migrations');
    }
  } catch (error: any) {
    logger.error('Erro ao configurar migrations:', error?.message || error);
    logger.warn('API continuará executando mesmo com erro na configuração das migrations');
  }
};

startMigrations();

iniciarProcessoCron();
