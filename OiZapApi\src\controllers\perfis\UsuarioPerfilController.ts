import { Request, Response } from 'express';
import { BAD_REQUEST, INTERNAL_SERVER_ERROR, parametrosInvalidos, erroInterno } from '../../interfaces/IRetorno';
import { UsuariosPerfisModel } from '../../models/perfis/UsuariosPerfisModel';

export class UsuarioPerfilController {
  static async associarUsuarioPerfil(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.cd_usuario) {
        errors.push('O campo "cd_usuario" é obrigatório.');
      }
      
      if (!req.body.cd_perfil) {
        errors.push('O campo "cd_perfil" é obrigatório.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new UsuariosPerfisModel().associar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async desassociarUsuarioPerfil(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.cd_usuario) {
        errors.push('O campo "cd_usuario" é obrigatório.');
      }
      
      if (!req.body.cd_perfil) {
        errors.push('O campo "cd_perfil" é obrigatório.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new UsuariosPerfisModel().desassociar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async listarAssociacoes(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new UsuariosPerfisModel().listar(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async buscarAssociacaoPorId(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.params.id) {
        errors.push('O parâmetro "id" é obrigatório.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new UsuariosPerfisModel().buscarPorId(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async listarPerfisPorUsuario(req: Request, res: Response): Promise<Response> {
    try {
      console.log('🔍 [DEBUG] UsuarioPerfilController.listarPerfisPorUsuario - Iniciando');
      console.log('🔍 [DEBUG] req.params:', req.params);
      console.log('🔍 [DEBUG] req.query:', req.query);

      const errors: string[] = [];

      if (!req.params.cd_usuario) {
        errors.push('O parâmetro "cd_usuario" é obrigatório.');
      }

      if (errors.length > 0) {
        console.log('❌ [DEBUG] UsuarioPerfilController.listarPerfisPorUsuario - Erros de validação:', errors);
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      console.log('🔍 [DEBUG] UsuarioPerfilController.listarPerfisPorUsuario - Chamando model');
      const result = await new UsuariosPerfisModel().listarPerfisPorUsuario(req);

      console.log('🔍 [DEBUG] UsuarioPerfilController.listarPerfisPorUsuario - Resultado:', {
        statuscode: result.statuscode,
        dataLength: result.data?.length || 0
      });

      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      console.error('❌ [DEBUG] UsuarioPerfilController.listarPerfisPorUsuario - Erro:', error);
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async listarUsuariosSemPerfil(req: Request, res: Response): Promise<Response> {
    try {
      const result = await new UsuariosPerfisModel().listarUsuariosSemPerfil(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async associarMultiplosPerfis(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.cd_usuario) {
        errors.push('O campo "cd_usuario" é obrigatório.');
      }
      
      if (!req.body.perfis || !Array.isArray(req.body.perfis)) {
        errors.push('O campo "perfis" deve ser um array.');
      }
      
      if (req.body.perfis && req.body.perfis.length === 0) {
        errors.push('O array "perfis" deve conter pelo menos um perfil.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new UsuariosPerfisModel().associarMultiplosPerfis(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }

  static async substituirPerfis(req: Request, res: Response): Promise<Response> {
    try {
      const errors: string[] = [];
      
      if (!req.body.cd_usuario) {
        errors.push('O campo "cd_usuario" é obrigatório.');
      }
      
      if (!req.body.perfis || !Array.isArray(req.body.perfis)) {
        errors.push('O campo "perfis" deve ser um array.');
      }
      
      if (errors.length > 0) {
        return res.status(BAD_REQUEST).send(parametrosInvalidos(errors));
      }

      const result = await new UsuariosPerfisModel().substituirPerfis(req);
      return res.status(result.statuscode).send(result);
    } catch (error: any) {
      return res.status(INTERNAL_SERVER_ERROR).send(erroInterno(error));
    }
  }
}
