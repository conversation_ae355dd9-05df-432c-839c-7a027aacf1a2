<template>
    <div class="container">
        <div class="error-page flex flex-col lg:flex-row items-center justify-center h-screen text-center lg:text-left">
            <div class="-intro-x lg:mr-20">
                <img alt="OiZap - Acesso Negado" class="h-48 lg:h-auto" src="@/assets/images/error-illustration.svg" />
            </div>
            <div class="text-white mt-10 lg:mt-0">
                <div class="intro-x text-8xl font-medium">403</div>
                <div class="intro-x text-xl lg:text-3xl font-medium mt-5">
                    Oops! Acesso Negado
                </div>
                <div class="intro-x text-lg mt-3">
                    Você não possui permissão para acessar esta funcionalidade.
                </div>
                <div v-if="rotaInfo" class="intro-x mt-4 p-4 bg-white/10 rounded-lg">
                    <div class="text-sm opacity-80">
                        <div><strong>Rota:</strong> {{ rotaInfo.rota }}</div>
                        <div><strong>Permissão necessária:</strong> {{ traduzirPermissao(rotaInfo.permissao) }}</div>
                    </div>
                </div>
                <div class="intro-x mt-8 flex flex-col sm:flex-row gap-4">
                    <button @click="voltarPagina" class="btn btn-primary py-3 px-4 w-full sm:w-auto">
                        <ArrowLeftIcon class="w-4 h-4 mr-2" />
                        Voltar
                    </button>
                    <button @click="irParaHome" class="btn btn-outline-secondary py-3 px-4 w-full sm:w-auto">
                        <HomeIcon class="w-4 h-4 mr-2" />
                        Ir para Home
                    </button>
                    <button @click="solicitarAcesso" class="btn btn-outline-warning py-3 px-4 w-full sm:w-auto">
                        <MailIcon class="w-4 h-4 mr-2" />
                        Solicitar Acesso
                    </button>
                </div>
                <div class="intro-x mt-6 text-sm opacity-70">
                    Se você acredita que deveria ter acesso a esta funcionalidade, 
                    entre em contato com o administrador do sistema.
                </div>
            </div>
        </div>

        <!-- Modal Solicitar Acesso -->
        <Modal :show="modalSolicitacao" @hidden="fecharModalSolicitacao">
            <ModalHeader>
                <h2 class="font-medium text-base mr-auto">Solicitar Acesso</h2>
            </ModalHeader>
            <ModalBody>
                <div class="grid grid-cols-12 gap-4 gap-y-3">
                    <div class="col-span-12">
                        <label class="form-label">Funcionalidade</label>
                        <input
                            :value="rotaInfo?.rota || ''"
                            type="text"
                            class="form-control"
                            readonly
                        />
                    </div>
                    <div class="col-span-12">
                        <label class="form-label">Permissão Necessária</label>
                        <input
                            :value="traduzirPermissao(rotaInfo?.permissao)"
                            type="text"
                            class="form-control"
                            readonly
                        />
                    </div>
                    <div class="col-span-12">
                        <label class="form-label">Justificativa *</label>
                        <textarea
                            v-model="formSolicitacao.justificativa"
                            class="form-control"
                            rows="4"
                            placeholder="Explique por que você precisa deste acesso..."
                            :class="{ 'border-danger': erros.justificativa }"
                        ></textarea>
                        <div v-if="erros.justificativa" class="text-danger text-xs mt-1">
                            {{ erros.justificativa }}
                        </div>
                    </div>
                </div>
            </ModalBody>
            <ModalFooter>
                <button @click="fecharModalSolicitacao" class="btn btn-outline-secondary w-20 mr-1">
                    Cancelar
                </button>
                <button @click="enviarSolicitacao" class="btn btn-primary w-20" :disabled="enviando">
                    <LoadingIcon v-if="enviando" class="w-4 h-4 mr-2" />
                    Enviar
                </button>
            </ModalFooter>
        </Modal>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// Estados
const modalSolicitacao = ref(false);
const enviando = ref(false);
const formSolicitacao = ref({
    justificativa: ''
});
const erros = ref({});

// Computed
const rotaInfo = computed(() => {
    if (route.query.rota && route.query.permissao) {
        return {
            rota: route.query.rota,
            permissao: route.query.permissao
        };
    }
    return null;
});

// Métodos
function traduzirPermissao(permissao) {
    const traducoes = {
        'visualizar': 'Visualização',
        'inserir': 'Criação/Inserção',
        'alterar': 'Edição/Alteração',
        'excluir': 'Exclusão/Remoção'
    };
    
    return traducoes[permissao] || permissao || 'Não especificada';
}

function voltarPagina() {
    if (window.history.length > 1) {
        router.go(-1);
    } else {
        irParaHome();
    }
}

function irParaHome() {
    router.push({ name: 'Home' });
}

function solicitarAcesso() {
    formSolicitacao.value = { justificativa: '' };
    erros.value = {};
    modalSolicitacao.value = true;
}

function fecharModalSolicitacao() {
    modalSolicitacao.value = false;
    formSolicitacao.value = { justificativa: '' };
    erros.value = {};
}

async function enviarSolicitacao() {
    try {
        enviando.value = true;
        erros.value = {};

        // Validações
        if (!formSolicitacao.value.justificativa?.trim()) {
            erros.value.justificativa = 'Justificativa é obrigatória';
            return;
        }

        if (formSolicitacao.value.justificativa.length < 10) {
            erros.value.justificativa = 'Justificativa deve ter pelo menos 10 caracteres';
            return;
        }

        // Aqui você pode implementar o envio da solicitação
        // Por exemplo, enviar email para administradores ou criar um ticket
        
        const dadosSolicitacao = {
            usuario: localStorage.getItem('usuario'),
            cd_usuario: localStorage.getItem('codusuario'),
            rota: rotaInfo.value?.rota,
            permissao: rotaInfo.value?.permissao,
            justificativa: formSolicitacao.value.justificativa,
            data_solicitacao: new Date().toISOString()
        };

        console.log('Solicitação de acesso:', dadosSolicitacao);
        
        // Simular envio
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mostrar sucesso
        alert('Solicitação enviada com sucesso! O administrador será notificado.');
        
        fecharModalSolicitacao();
    } catch (error) {
        console.error('Erro ao enviar solicitação:', error);
        alert('Erro ao enviar solicitação. Tente novamente.');
    } finally {
        enviando.value = false;
    }
}

// Lifecycle
onMounted(() => {
    // Registrar evento para analytics/logs
    console.log('Acesso negado:', {
        rota: rotaInfo.value?.rota,
        permissao: rotaInfo.value?.permissao,
        usuario: localStorage.getItem('usuario'),
        timestamp: new Date().toISOString()
    });
});
</script>

<style scoped>
.error-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    padding: 2rem;
}

@media (max-width: 640px) {
    .error-page {
        padding: 1rem;
    }
}
</style>
